/* Modern Theme Custom Styles */

/* Base theme styles */
.modern-theme {
    background-color: #000000;
    color: #ffffff;
    font-family: system-ui, -apple-system, sans-serif;
}

/* Gradient text effects */
.modern-gradient-text-primary {
    background: linear-gradient(to right, #ffffff, #f3f4f6, #d1d5db);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 900;
}

.modern-gradient-text-secondary {
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-gradient-text-accent {
    background: linear-gradient(to right, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 600;
}

/* Button styles */
.modern-button-primary {
    background: linear-gradient(to right, #9333ea, #db2777);
    color: #ffffff;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 180px;
    justify-content: center;
}

.modern-button-primary:hover {
    background: linear-gradient(to right, #7c3aed, #be185d);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
}

/* Container styles */
.modern-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .modern-container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-container {
        padding: 0 2rem;
    }
}

/* Hero section styles */
.modern-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.modern-hero-title {
    font-size: 3rem;
    line-height: 1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
}

@media (min-width: 640px) {
    .modern-hero-title {
        font-size: 3.75rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-title {
        font-size: 4.5rem;
    }
}

@media (min-width: 1280px) {
    .modern-hero-title {
        font-size: 6rem;
    }
}

.modern-hero-subtitle {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 2rem;
    letter-spacing: 0.025em;
}

@media (min-width: 640px) {
    .modern-hero-subtitle {
        font-size: 1.875rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-subtitle {
        font-size: 2.25rem;
    }
}

/* Project section styles */
.modern-project-title {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
    color: #ffffff;
}

@media (min-width: 1024px) {
    .modern-project-title {
        font-size: 2.25rem;
    }
}

.modern-project-description {
    font-size: 1.125rem;
    color: #d1d5db;
    line-height: 1.6;
}

/* Footer styles */
.modern-footer {
    background-color: #000000;
    border-top: 1px solid #374151;
    position: relative;
    overflow: hidden;
    padding: 1rem;
}

.modern-footer-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
}

@media (min-width: 640px) {
    .modern-footer-title {
        font-size: 1.875rem;
    }
}

.modern-footer-subtitle {
    font-size: 1.125rem;
}

.modern-footer-text {
    color: #9ca3af;
    font-size: 0.875rem;
}

.modern-footer-brand {
    color: #6b7280;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.modern-footer-brand-name {
    font-weight: 600;
}

/* Navbar styles */
.modern-navbar {
    position: sticky;
    top: 0;
    z-index: 50;
    width: 100%;
    border-bottom: 1px solid #374151;
    background-color: rgba(3, 7, 18, 0.95);
    backdrop-filter: blur(8px);
    color: #ffffff;
}

.modern-navbar-container {
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .modern-navbar-container {
        padding: 0 1.5rem;
    }
}

.modern-navbar-brand {
    font-weight: 700;
    letter-spacing: -0.025em;
    text-decoration: none;
    color: #ffffff;
}

.modern-navbar-nav {
    display: none;
    gap: 1.5rem;
    align-items: center;
}

@media (min-width: 768px) {
    .modern-navbar-nav {
        display: flex;
    }
}

.modern-navbar-link {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-navbar-link:hover {
    color: #a855f7;
}

/* Mobile menu styles */
.modern-mobile-menu {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    font-size: 1.125rem;
    font-weight: 500;
    margin-top: 2rem;
    align-items: center;
    text-align: center;
}

.modern-mobile-menu-link {
    font-size: 1.125rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-mobile-menu-link:hover {
    color: #a855f7;
}

/* Responsive utilities */
.modern-section {
    padding: 5rem 0;
}

@media (min-width: 1024px) {
    .modern-section {
        padding: 8rem 0;
    }
}

/* Animation utilities */
.modern-fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure proper text rendering */
.modern-theme * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
