/* Modern Theme Custom Styles */

/* Base theme styles */
.modern-theme {
    background-color: #000000;
    color: #ffffff;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Reset and base styles */
.modern-theme * {
    box-sizing: border-box;
}

.modern-theme a {
    text-decoration: none;
    color: inherit;
}

.modern-theme button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
}

/* Gradient text effects */
.modern-gradient-text-primary {
    background: linear-gradient(to right, #ffffff, #f3f4f6, #d1d5db);
    -webkit-background-clip: text;
    background-clip: text;
    color: #ffffff; /* Fallback for exports */
    font-weight: 900;
}

/* Ensure gradient text works in exports */
@supports (-webkit-background-clip: text) {
    .modern-gradient-text-primary {
        color: transparent;
    }
}

.modern-gradient-text-secondary {
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    -webkit-background-clip: text;
    background-clip: text;
    color: #c4b5fd; /* Fallback for exports */
}

@supports (-webkit-background-clip: text) {
    .modern-gradient-text-secondary {
        color: transparent;
    }
}

.modern-gradient-text-accent {
    background: linear-gradient(to right, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    background-clip: text;
    color: #c4b5fd; /* Fallback for exports */
    font-weight: 600;
}

@supports (-webkit-background-clip: text) {
    .modern-gradient-text-accent {
        color: transparent;
    }
}

.modern-gradient-text-orange {
    background: linear-gradient(to right, #f97316, #ea580c);
    -webkit-background-clip: text;
    background-clip: text;
    color: #fdba74; /* Fallback for exports */
    font-weight: 600;
}

@supports (-webkit-background-clip: text) {
    .modern-gradient-text-orange {
        color: transparent;
    }
}

.modern-gradient-text-cyan {
    background: linear-gradient(to right, #06b6d4, #0891b2);
    -webkit-background-clip: text;
    background-clip: text;
    color: #67e8f9; /* Fallback for exports */
    font-weight: 600;
}

@supports (-webkit-background-clip: text) {
    .modern-gradient-text-cyan {
        color: transparent;
    }
}

.modern-gradient-text-green {
    background: linear-gradient(to right, #10b981, #059669);
    -webkit-background-clip: text;
    background-clip: text;
    color: #86efac; /* Fallback for exports */
    font-weight: 600;
}

@supports (-webkit-background-clip: text) {
    .modern-gradient-text-green {
        color: transparent;
    }
}

/* Button styles */
.modern-button-primary {
    background: linear-gradient(to right, #9333ea, #db2777);
    color: #ffffff;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 180px;
    justify-content: center;
    font-size: 1rem;
    line-height: 1.5;
}

.modern-button-primary:hover {
    background: linear-gradient(to right, #7c3aed, #be185d);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
}

.modern-button-secondary {
    background: transparent;
    color: #ffffff;
    padding: 0.75rem 2rem;
    border: 2px solid #374151;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 180px;
    justify-content: center;
    font-size: 1rem;
    line-height: 1.5;
}

.modern-button-secondary:hover {
    border-color: #a855f7;
    background: rgba(168, 85, 247, 0.1);
    transform: translateY(-2px);
}

/* Container styles */
.modern-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .modern-container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-container {
        padding: 0 2rem;
    }
}

/* Hero section styles */
.modern-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: #000000;
    padding: 5rem 1rem;
}

.modern-hero-background {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.1) 0%, transparent 50%);
}

.modern-hero-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 120px 120px;
}

.modern-hero-container {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    padding: 5rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    min-height: 100vh;
}

@media (min-width: 1024px) {
    .modern-hero-container {
        flex-direction: row;
        gap: 5rem;
    }
}

.modern-hero-content {
    text-align: center;
    max-width: 32rem;
}

@media (min-width: 1024px) {
    .modern-hero-content {
        text-align: left;
    }
}

.modern-hero-profile {
    position: relative;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modern-hero-profile-container {
    position: relative;
    width: 16rem;
    height: 16rem;
}

@media (min-width: 1024px) {
    .modern-hero-profile-container {
        width: 20rem;
        height: 20rem;
    }
}

.modern-hero-profile-glow {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    border-radius: 50%;
    filter: blur(0.75rem);
    opacity: 0.75;
    transition: opacity 0.5s ease;
}

.modern-hero-profile:hover .modern-hero-profile-glow {
    opacity: 1;
}

.modern-hero-profile-inner {
    position: absolute;
    inset: 0.5rem;
    background-color: #000000;
    border-radius: 50%;
}

.modern-hero-profile-image {
    position: absolute;
    inset: 0.75rem;
    border-radius: 50%;
    object-fit: cover;
    width: calc(100% - 1.5rem);
    height: calc(100% - 1.5rem);
}

.modern-hero-title {
    font-size: 2rem;
    line-height: 1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
    font-weight: 900;
}

@media (min-width: 640px) {
    .modern-hero-title {
        font-size: 3.75rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-title {
        font-size: 4.5rem;
    }
}

@media (min-width: 1280px) {
    .modern-hero-title {
        font-size: 6rem;
    }
}

.modern-hero-subtitle {
    font-size: 1.125rem;
    font-weight: 400;
    margin-bottom: 2rem;
    letter-spacing: 0.025em;
    line-height: 1.5;
    max-width: 28rem;
    margin-left: auto;
    margin-right: auto;
}

@media (min-width: 640px) {
    .modern-hero-subtitle {
        font-size: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-subtitle {
        font-size: 1.875rem;
        margin-left: 0;
        margin-right: 0;
    }
}

.modern-hero-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    margin-bottom: 3rem;
}

@media (min-width: 640px) {
    .modern-hero-buttons {
        flex-direction: row;
        justify-content: center;
    }
}

@media (min-width: 1024px) {
    .modern-hero-buttons {
        justify-content: flex-start;
    }
}

.modern-hero-scroll-indicator {
    display: flex;
    justify-content: center;
    animation: bounce 2s infinite;
}

.modern-hero-scroll-mouse {
    width: 1.5rem;
    height: 2.5rem;
    border: 2px solid #6b7280;
    border-radius: 1.25rem;
    display: flex;
    justify-content: center;
    position: relative;
}

.modern-hero-scroll-dot {
    width: 0.25rem;
    height: 0.75rem;
    background-color: #9ca3af;
    border-radius: 0.125rem;
    margin-top: 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Section styles */
.modern-section {
    padding: 5rem 0;
    position: relative;
}

@media (min-width: 1024px) {
    .modern-section {
        padding: 8rem 0;
    }
}

.modern-section-background {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.05) 0%, transparent 70%);
}

.modern-section-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.01) 1px, transparent 1px);
    background-size: 120px 120px;
}

.modern-section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.modern-section-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1rem;
    line-height: 1.1;
}

@media (min-width: 640px) {
    .modern-section-title {
        font-size: 4rem;
    }
}

@media (min-width: 1024px) {
    .modern-section-title {
        font-size: 5rem;
    }
}

@media (min-width: 1280px) {
    .modern-section-title {
        font-size: 6rem;
    }
}

.modern-section-divider {
    width: 6rem;
    height: 0.25rem;
    background: linear-gradient(to right, #a855f7, #ec4899);
    margin: 0 auto;
    border-radius: 0.125rem;
}

/* About section styles */
.modern-about-content {
    display: grid;
    gap: 3rem;
    align-items: center;
}

@media (min-width: 1024px) {
    .modern-about-content {
        grid-template-columns: 1fr 1fr;
        gap: 5rem;
    }
}

.modern-about-text {
    position: relative;
}

.modern-about-quote {
    position: absolute;
    top: -1rem;
    left: -1rem;
    font-size: 4rem;
    color: #ffffff;
    font-family: serif;
    line-height: 1;
}

.modern-about-description {
    font-size: 1rem;
    color: #d1d5db;
    line-height: 1.75;
    position: relative;
    z-index: 10;
    padding: 1rem;
    background: rgba(17, 24, 39, 0.3);
    border-radius: 1rem;
    border: 1px solid rgba(55, 65, 81, 0.3);
}

@media (min-width: 640px) {
    .modern-about-description {
        font-size: 1.125rem;
        padding: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-about-description {
        font-size: 1.25rem;
        padding: 2rem;
    }
}

.modern-about-skills {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.modern-about-skills-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.modern-about-skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.modern-about-skill {
    background: rgba(55, 65, 81, 0.3);
    border: 1px solid #374151;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.modern-about-skill:hover {
    border-color: #a855f7;
    background: rgba(168, 85, 247, 0.1);
    transform: translateY(-2px);
}

.modern-about-skill-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #ffffff;
}

/* About section quote and stats */
.modern-about-quote-container {
    position: relative;
    padding: 2rem 0;
}

.modern-about-quote-mark {
    position: absolute;
    top: -1rem;
    left: -1rem;
    font-size: 4rem;
    color: rgba(168, 85, 247, 0.3);
    font-family: serif;
    line-height: 1;
    z-index: 1;
}

.modern-about-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.modern-about-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .modern-about-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.modern-about-stat-card {
    background: rgba(17, 24, 39, 0.5);
    border: 1px solid rgba(55, 65, 81, 0.5);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modern-about-stat-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, transparent, rgba(255,255,255,0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-about-stat-card:hover::before {
    opacity: 1;
}

.modern-about-stat-card:hover {
    transform: translateY(-4px);
    border-color: rgba(168, 85, 247, 0.5);
}

.modern-about-stat-number {
    font-size: 2rem;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
    .modern-about-stat-number {
        font-size: 2.5rem;
    }
}

.modern-about-stat-label {
    font-size: 0.875rem;
    color: #9ca3af;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-about-values {
    margin-top: 2rem;
}

.modern-about-values-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.modern-about-values-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.modern-about-value-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modern-about-value-dot {
    width: 0.5rem;
    height: 0.5rem;
    background: linear-gradient(to right, #a855f7, #ec4899);
    border-radius: 50%;
    flex-shrink: 0;
}

.modern-about-value-text {
    font-size: 0.875rem;
    color: #d1d5db;
    font-weight: 500;
}

/* Project section styles */
.modern-projects-grid {
    display: flex;
    flex-direction: column;
    gap: 6rem;
}

.modern-project {
    display: grid;
    gap: 3rem;
    align-items: center;
}

@media (min-width: 1024px) {
    .modern-project {
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
    }
}

.modern-project:nth-child(even) .modern-project-content {
    order: 2;
}

@media (min-width: 1024px) {
    .modern-project:nth-child(even) .modern-project-image {
        order: 1;
    }
}

.modern-project-image {
    position: relative;
}

.modern-project-image-container {
    position: relative;
    aspect-ratio: 4 / 3;
    border-radius: 1rem;
    overflow: hidden;
    background: #111827;
    transition: all 0.5s ease;
}

.modern-project:hover .modern-project-image-container {
    box-shadow: 0 25px 50px rgba(168, 85, 247, 0.2);
}

.modern-project-image-glow {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 1rem;
    filter: blur(0.5rem);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.modern-project:hover .modern-project-image-glow {
    opacity: 1;
}

.modern-project-image-inner {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 1rem;
    overflow: hidden;
    z-index: 10;
}

.modern-project-image-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modern-project-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.modern-project-title {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
    color: #ffffff;
}

@media (min-width: 1024px) {
    .modern-project-title {
        font-size: 2.25rem;
    }
}

.modern-project-description {
    font-size: 1.125rem;
    color: #d1d5db;
    line-height: 1.6;
}

.modern-project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.modern-project-tech-item {
    background: rgba(168, 85, 247, 0.1);
    color: #a855f7;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid rgba(168, 85, 247, 0.3);
}

.modern-project-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.modern-project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: 1px solid #374151;
    border-radius: 0.375rem;
    color: #d1d5db;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.modern-project-link:hover {
    border-color: #a855f7;
    color: #a855f7;
    background: rgba(168, 85, 247, 0.1);
}

/* Contact section styles */
.modern-contact {
    text-align: center;
    position: relative;
    overflow: hidden;
}

.modern-contact-background {
    position: absolute;
    inset: 0;
}

.modern-contact-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.01) 1px, transparent 1px);
    background-size: 120px 120px;
}

.modern-contact-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-contact-content {
    position: relative;
    z-index: 10;
}

.modern-contact-header {
    margin-bottom: 4rem;
}

.modern-contact-title-primary {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

@media (min-width: 640px) {
    .modern-contact-title-primary {
        font-size: 4rem;
    }
}

@media (min-width: 1024px) {
    .modern-contact-title-primary {
        font-size: 5rem;
    }
}

@media (min-width: 1280px) {
    .modern-contact-title-primary {
        font-size: 6rem;
    }
}

.modern-contact-title-secondary {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 2rem;
    line-height: 1.1;
}

@media (min-width: 640px) {
    .modern-contact-title-secondary {
        font-size: 4rem;
    }
}

@media (min-width: 1024px) {
    .modern-contact-title-secondary {
        font-size: 5rem;
    }
}

@media (min-width: 1280px) {
    .modern-contact-title-secondary {
        font-size: 6rem;
    }
}

.modern-contact-description {
    font-size: 1.25rem;
    color: #9ca3af;
    max-width: 48rem;
    margin: 0 auto;
    line-height: 1.75;
    margin-bottom: 2rem;
}

.modern-contact-card {
    max-width: 32rem;
    margin: 0 auto 4rem;
}

.modern-contact-card-inner {
    background: rgba(17, 24, 39, 0.5);
    -webkit-backdrop-filter: blur(0.5rem);
    backdrop-filter: blur(0.5rem);
    border: 1px solid #374151;
    border-radius: 1.5rem;
    padding: 2rem;
    transition: all 0.5s ease;
}

@media (min-width: 1024px) {
    .modern-contact-card-inner {
        padding: 3rem;
    }
}

.modern-contact-card-inner:hover {
    border-color: rgba(168, 85, 247, 0.5);
}

.modern-contact-email-section {
    margin-bottom: 2rem;
}

.modern-contact-email-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #d1d5db;
    margin-bottom: 1rem;
}

.modern-contact-email-link {
    display: inline-flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.3s ease;
}

@media (min-width: 1024px) {
    .modern-contact-email-link {
        font-size: 1.25rem;
    }
}

.modern-contact-email-arrow {
    margin-left: 0.5rem;
    width: 1rem;
    height: 1rem;
    color: #a855f7;
    transition: transform 0.3s ease;
}

.modern-contact-email-link:hover .modern-contact-email-arrow {
    transform: translateX(0.5rem);
}

.modern-contact-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #6b7280, transparent);
    margin: 2rem 0;
}

.modern-contact-social-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.modern-contact-social-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #d1d5db;
}

.modern-contact-social-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.25rem;
}

@media (min-width: 768px) {
    .modern-contact-social-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.modern-contact-social-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.modern-contact-social-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.75rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.modern-contact-social-link:hover {
    background: rgba(55, 65, 81, 0.5);
}

.modern-contact-social-icon-container {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modern-contact-social-icon {
    width: 1.75rem;
    height: 1.75rem;
    color: #9ca3af;
    transition: color 0.3s ease;
}

.modern-contact-social-link:hover .modern-contact-social-icon {
    color: #ffffff;
}

.modern-contact-social-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #9ca3af;
    transition: color 0.3s ease;
}

.modern-contact-social-link:hover .modern-contact-social-label {
    color: #ffffff;
}

.modern-contact-cta {
    text-align: center;
}

.modern-contact-cta-text {
    font-size: 1.125rem;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.modern-contact-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #a855f7;
}

.modern-contact-status-dot {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #a855f7;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.modern-contact-status-text {
    font-size: 0.875rem;
    font-weight: 500;
}

.modern-contact-status-dot:last-child {
    animation-delay: 1s;
}

/* Footer styles */
.modern-footer {
    background-color: #000000;
    border-top: 1px solid #374151;
    position: relative;
    overflow: hidden;
    padding: 1rem;
}

.modern-footer-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
}

@media (min-width: 640px) {
    .modern-footer-title {
        font-size: 1.875rem;
    }
}

.modern-footer-subtitle {
    font-size: 1.125rem;
}

.modern-footer-text {
    color: #9ca3af;
    font-size: 0.875rem;
}

.modern-footer-brand {
    color: #6b7280;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.modern-footer-brand-name {
    font-weight: 600;
}

/* Navbar styles */
.modern-navbar {
    position: sticky;
    top: 0;
    z-index: 50;
    width: 100%;
    border-bottom: 1px solid #374151;
    background-color: rgba(3, 7, 18, 0.95);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    color: #ffffff;
}

.modern-navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .modern-navbar-container {
        padding: 0 1.5rem;
    }
}

.modern-navbar-brand {
    font-weight: 700;
    letter-spacing: -0.025em;
    text-decoration: none;
    color: #ffffff;
    font-size: 1.125rem;
    transition: color 0.3s ease;
}

.modern-navbar-brand:hover {
    color: #a855f7;
}

.modern-navbar-nav {
    display: none;
    gap: 1.5rem;
    align-items: center;
}

@media (min-width: 768px) {
    .modern-navbar-nav {
        display: flex;
    }
}

.modern-navbar-link {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.modern-navbar-link:hover {
    color: #a855f7;
}

.modern-navbar-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, #a855f7, #ec4899);
    transition: width 0.3s ease;
}

.modern-navbar-link:hover::after {
    width: 100%;
}

/* Mobile menu button */
.modern-mobile-menu-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: 0.375rem;
    color: #ffffff;
    transition: all 0.3s ease;
    background: transparent;
    border: none;
    cursor: pointer;
}

@media (min-width: 768px) {
    .modern-mobile-menu-button {
        display: none;
    }
}

.modern-mobile-menu-button:hover {
    background-color: #374151;
    color: #ffffff;
}

/* Mobile menu panel */
.modern-mobile-menu-panel {
    background-color: rgba(3, 7, 18, 0.95);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid #374151;
}

.modern-mobile-menu-nav {
    padding: 1rem 1rem 1.5rem;
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-mobile-menu-link {
    display: block;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    color: #d1d5db;
    text-decoration: none;
    transition: all 0.3s ease;
}

.modern-mobile-menu-link:hover {
    background-color: #374151;
    color: #ffffff;
}

/* Sheet/Modal styles for mobile menu */
.modern-sheet-content {
    background-color: #030712;
    color: #ffffff;
    border-left: 1px solid #374151;
    width: 320px;
}

.modern-mobile-menu {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    font-size: 1.125rem;
    font-weight: 500;
    margin-top: 2rem;
    align-items: center;
    text-align: center;
}

.modern-mobile-menu .modern-navbar-link {
    font-size: 1.125rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
    padding: 0.5rem 0;
}

.modern-mobile-menu .modern-navbar-link:hover {
    color: #a855f7;
}

/* Utility classes */
.modern-hidden {
    display: none;
}

.modern-block {
    display: block;
}

.modern-flex {
    display: flex;
}

.modern-grid {
    display: grid;
}

.modern-relative {
    position: relative;
}

.modern-absolute {
    position: absolute;
}

.modern-text-center {
    text-align: center;
}

.modern-text-left {
    text-align: left;
}

.modern-text-white {
    color: #ffffff;
}

.modern-text-gray-300 {
    color: #d1d5db;
}

.modern-text-gray-400 {
    color: #9ca3af;
}

.modern-text-purple-300 {
    color: #d8b4fe;
}

.modern-text-purple-400 {
    color: #c084fc;
}

.modern-bg-transparent {
    background-color: transparent;
}

.modern-bg-gray-800 {
    background-color: #1f2937;
}

.modern-bg-gray-900 {
    background-color: #111827;
}

.modern-border-gray-700 {
    border-color: #374151;
}

.modern-border-gray-800 {
    border-color: #1f2937;
}

.modern-rounded {
    border-radius: 0.25rem;
}

.modern-rounded-lg {
    border-radius: 0.5rem;
}

.modern-rounded-xl {
    border-radius: 0.75rem;
}

.modern-p-3 {
    padding: 0.75rem;
}

.modern-p-4 {
    padding: 1rem;
}

.modern-px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.modern-py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.modern-mb-4 {
    margin-bottom: 1rem;
}

.modern-mt-8 {
    margin-top: 2rem;
}

.modern-gap-2 {
    gap: 0.5rem;
}

.modern-gap-3 {
    gap: 0.75rem;
}

.modern-gap-5 {
    gap: 1.25rem;
}

.modern-w-4 {
    width: 1rem;
}

.modern-h-4 {
    height: 1rem;
}

.modern-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.modern-font-medium {
    font-weight: 500;
}

.modern-font-semibold {
    font-weight: 600;
}

.modern-font-bold {
    font-weight: 700;
}

.modern-transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.modern-transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.modern-duration-300 {
    transition-duration: 300ms;
}

.modern-hover-bg-gray-800:hover {
    background-color: #1f2937;
}

.modern-hover-text-white:hover {
    color: #ffffff;
}

.modern-hover-text-purple-400:hover {
    color: #c084fc;
}

/* Responsive utilities */
@media (min-width: 768px) {
    .modern-md-hidden {
        display: none;
    }

    .modern-md-flex {
        display: flex;
    }

    .modern-md-grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

/* Animation utilities */
.modern-fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form input styles */
.modern-input {
    background-color: rgba(55, 65, 81, 0.5);
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #374151;
    color: #d1d5db;
    font-size: 0.875rem;
    width: 100%;
    transition: all 0.3s ease;
}

.modern-input:focus {
    outline: none;
    border-color: #a855f7;
    background-color: rgba(55, 65, 81, 0.7);
}

/* Custom hamburger menu styles */
.modern-hamburger-menu {
    position: relative;
    z-index: 60;
}

.modern-hamburger-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 320px;
    height: 100vh;
    background-color: #030712;
    border-left: 1px solid #374151;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 50;
}

.modern-hamburger-panel.modern-open {
    transform: translateX(0);
}

.modern-hamburger-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 40;
}

.modern-hamburger-overlay.modern-open {
    opacity: 1;
    visibility: visible;
}

/* Loading overlay styles */
.modern-loading-overlay {
    position: absolute;
    inset: 0.5rem;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 30;
    -webkit-backdrop-filter: blur(0.5rem);
    backdrop-filter: blur(0.5rem);
}

.modern-loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #ffffff;
}

.modern-loading-spinner {
    position: relative;
}

.modern-loading-spinner-ring {
    width: 4rem;
    height: 4rem;
    border: 4px solid rgba(168, 85, 247, 0.3);
    border-radius: 50%;
}

.modern-loading-spinner-active {
    position: absolute;
    inset: 0;
    width: 4rem;
    height: 4rem;
    border: 4px solid #a855f7;
    border-top: 4px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.modern-loading-text {
    text-align: center;
}

.modern-loading-title {
    font-size: 1.125rem;
    font-weight: 600;
}

.modern-loading-subtitle {
    font-size: 0.875rem;
    color: #d1d5db;
}

/* Upload overlay styles */
.modern-upload-overlay {
    position: absolute;
    inset: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    z-index: 20;
    -webkit-backdrop-filter: blur(0.5rem);
    backdrop-filter: blur(0.5rem);
}

.modern-hero-profile:hover .modern-upload-overlay {
    opacity: 1;
}

.modern-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.modern-upload-icon {
    width: 2rem;
    height: 2rem;
}

.modern-upload-text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Button content styles */
.modern-button-content {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.modern-button-icon {
    margin-right: 0.5rem;
}

.modern-button-icon-spinning {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

/* ===== PROJECTS SECTION ===== */
.modern-projects-section {
    background: rgb(3 7 18);
    color: white;
    overflow: hidden;
}

.modern-projects-grid-bg {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px);
    background-size: 80px 80px;
    animation: pulse 4s ease-in-out infinite;
}

.modern-projects-container {
    display: flex;
    flex-direction: column;
    gap: 6rem;
}

@media (min-width: 1024px) {
    .modern-projects-container {
        gap: 8rem;
    }
}

.modern-project-item {
    position: relative;
}

.modern-project-item:hover .modern-project-image-glow {
    opacity: 1;
}

.modern-project-item:hover .modern-project-image {
    transform: scale(1.1);
}

.modern-project-item:hover .modern-project-image-overlay {
    opacity: 1;
}

.modern-project-grid {
    display: grid;
    gap: 3rem;
    align-items: center;
}

@media (min-width: 1024px) {
    .modern-project-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 4rem;
    }
}

.modern-project-grid-reverse {
    grid-auto-flow: column dense;
}

.modern-project-image-container {
    position: relative;
}

.modern-project-image-reverse {
    grid-column-start: 2;
}

.modern-project-image-wrapper {
    position: relative;
    aspect-ratio: 4/3;
    border-radius: 1rem;
    overflow: hidden;
    background: rgb(17 24 39);
    transition: all 0.5s ease;
}

.modern-project-image-wrapper:hover {
    box-shadow: 0 25px 50px -12px rgba(147 51 234 / 0.2);
}

.modern-project-image-glow {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(147 51 234 / 0.2), rgba(236 72 153 / 0.2), rgba(6 182 212 / 0.2));
    border-radius: 1rem;
    filter: blur(4px);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.modern-project-image-inner {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 100%;
    border-radius: 1rem;
    overflow: hidden;
}

.modern-project-image {
    object-fit: cover;
    transition: all 0.7s ease;
}

.modern-project-image-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.6), transparent, transparent);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.modern-project-loading {
    border-radius: 1rem;
    z-index: 40;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.modern-project-upload {
    border-radius: 1rem;
    z-index: 20;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.modern-project-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.modern-project-content-reverse {
    grid-column-start: 1;
}

.modern-project-header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-project-number {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modern-project-number-line {
    width: 2rem;
    height: 1px;
    background: linear-gradient(to right, rgb(147 51 234), rgb(236 72 153));
}

.modern-project-number-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(156 163 175);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-project-title {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
}

@media (min-width: 1024px) {
    .modern-project-title {
        font-size: 2.25rem;
    }
}

.modern-project-description {
    font-size: 1.125rem;
    color: rgb(209 213 219);
    line-height: 1.6;
}

.modern-project-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top: 1rem;
}

@media (min-width: 640px) {
    .modern-project-actions {
        flex-direction: row;
    }
}

.modern-project-url-edit {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.modern-project-url-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgb(156 163 175);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-project-url-input {
    font-size: 0.875rem;
    color: rgb(196 181 253);
    background: rgba(17 24 39 / 0.5);
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid rgb(31 41 55);
}

.modern-projects-add {
    text-align: center;
    margin-top: 5rem;
}

/* ===== CONTACT SECTION ===== */
.modern-contact-section {
    background: rgb(0 0 0);
    color: white;
    overflow: hidden;
}

.modern-contact-grid-bg {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.01) 1px, transparent 1px);
    background-size: 120px 120px;
}

.modern-contact-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(147 51 234 / 0.2), rgba(236 72 153 / 0.2), rgba(6 182 212 / 0.2));
    border-radius: 50%;
    filter: blur(48px);
}

.modern-contact-subtitle {
    font-size: 2.25rem;
    font-weight: 900;
    margin-bottom: 2rem;
}

@media (min-width: 640px) {
    .modern-contact-subtitle {
        font-size: 3rem;
    }
}

@media (min-width: 1024px) {
    .modern-contact-subtitle {
        font-size: 3.75rem;
    }
}

.modern-contact-card-container {
    max-width: 42rem;
    margin: 0 auto 4rem auto;
}

.modern-contact-card {
    background: rgba(17 24 39 / 0.5);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    border: 1px solid rgb(31 41 55);
    border-radius: 1.5rem;
    padding: 2rem;
    transition: all 0.5s ease;
}

@media (min-width: 1024px) {
    .modern-contact-card {
        padding: 3rem;
    }
}

.modern-contact-card:hover {
    border-color: rgba(147 51 234 / 0.5);
}

.modern-contact-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.modern-contact-email-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-contact-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: rgb(209 213 219);
    margin-bottom: 1rem;
}

.modern-contact-email-link {
    display: inline-flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 700;
    transition: all 0.3s ease;
}

@media (min-width: 1024px) {
    .modern-contact-email-link {
        font-size: 1.25rem;
    }
}

.modern-contact-email-arrow {
    margin-left: 0.5rem;
    width: 1rem;
    height: 1rem;
    color: rgb(196 181 253);
    transition: transform 0.3s ease;
}

.modern-contact-email-link:hover .modern-contact-email-arrow {
    transform: translateX(0.5rem);
}

.modern-contact-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgb(55 65 81), transparent);
}

.modern-contact-social-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.modern-contact-social-edit {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.25rem;
}

@media (min-width: 768px) {
    .modern-contact-social-edit {
        grid-template-columns: repeat(3, 1fr);
    }
}

.modern-contact-social-field {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.modern-contact-social-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(156 163 175);
}

.modern-contact-social-input {
    font-size: 0.875rem;
    background: rgba(31 41 55 / 0.5);
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid rgb(55 65 81);
    color: rgb(209 213 219);
}

.modern-contact-social-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.modern-contact-social-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.modern-contact-social-link:hover {
    background: rgba(31 41 55 / 0.5);
}

.modern-contact-social-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modern-contact-social-github {
    background: linear-gradient(to right, rgba(147 51 234 / 0.2), rgba(236 72 153 / 0.2));
}

.modern-contact-social-link:hover .modern-contact-social-github {
    background: linear-gradient(to right, rgba(147 51 234 / 0.3), rgba(236 72 153 / 0.3));
}

.modern-contact-social-linkedin {
    background: linear-gradient(to right, rgba(6 182 212 / 0.2), rgba(59 130 246 / 0.2));
}

.modern-contact-social-link:hover .modern-contact-social-linkedin {
    background: linear-gradient(to right, rgba(6 182 212 / 0.3), rgba(59 130 246 / 0.3));
}

.modern-contact-social-twitter {
    background: linear-gradient(to right, rgba(34 197 94 / 0.2), rgba(16 185 129 / 0.2));
}

.modern-contact-social-link:hover .modern-contact-social-twitter {
    background: linear-gradient(to right, rgba(34 197 94 / 0.3), rgba(16 185 129 / 0.3));
}

.modern-contact-social-icon-svg {
    width: 1.75rem;
    height: 1.75rem;
    color: rgb(156 163 175);
    transition: color 0.3s ease;
}

.modern-contact-social-link:hover .modern-contact-social-icon-svg {
    color: white;
}

.modern-contact-social-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(156 163 175);
    transition: color 0.3s ease;
}

.modern-contact-social-link:hover .modern-contact-social-text {
    color: white;
}

.modern-contact-cta {
    text-align: center;
}

.modern-contact-cta-text {
    font-size: 1.125rem;
    color: rgb(156 163 175);
    margin-bottom: 1rem;
}

.modern-contact-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: rgb(196 181 253);
}

.modern-contact-status-dot {
    width: 0.5rem;
    height: 0.5rem;
    background: rgb(196 181 253);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.modern-contact-status-dot-2 {
    animation-delay: 1s;
}

.modern-contact-status-text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* ===== FOOTER SECTION ===== */
.modern-footer {
    position: relative;
    background: rgb(0 0 0);
    color: white;
}

.modern-footer-background {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.modern-footer-glow-left {
    position: absolute;
    bottom: 0;
    left: 5rem;
    width: 16rem;
    height: 16rem;
    background: linear-gradient(to right, rgba(147 51 234 / 0.1), rgba(236 72 153 / 0.1));
    border-radius: 50%;
    filter: blur(48px);
}

.modern-footer-glow-right {
    position: absolute;
    bottom: 0;
    right: 5rem;
    width: 12rem;
    height: 12rem;
    background: linear-gradient(to right, rgba(59 130 246 / 0.1), rgba(6 182 212 / 0.1));
    border-radius: 50%;
    filter: blur(48px);
}

.modern-footer-content {
    position: relative;
    z-index: 10;
    padding: 3rem 0;
}

.modern-footer-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 2rem;
}

.modern-footer-main {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-footer-title {
    font-size: 1.5rem;
    font-weight: 700;
}

.modern-footer-subtitle {
    font-size: 1.125rem;
    font-weight: 500;
}

.modern-footer-divider {
    width: 6rem;
    height: 1px;
    background: linear-gradient(to right, transparent, rgb(75 85 99), transparent);
}

.modern-footer-bottom {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
}

.modern-footer-text {
    font-size: 0.875rem;
    color: rgb(156 163 175);
}

.modern-footer-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: rgb(156 163 175);
}

.modern-footer-heart {
    width: 1rem;
    height: 1rem;
    color: rgb(239 68 68);
}

.modern-footer-brand:hover .modern-footer-heart {
    animation: pulse 1s ease-in-out infinite;
}

.modern-footer-brand-icons {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.modern-footer-code-icon {
    width: 1rem;
    height: 1rem;
    color: rgb(196 181 253);
}

.modern-footer-brand-name {
    font-weight: 600;
}

.modern-footer-sparkles-icon {
    width: 1rem;
    height: 1rem;
    color: rgb(34 211 238);
}

.modern-footer-back-to-top {
    padding-top: 1rem;
}

.modern-footer-back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: rgb(107 114 128);
    transition: color 0.3s ease;
}

.modern-footer-back-link:hover {
    color: rgb(196 181 253);
}

.modern-footer-back-icon {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    border: 1px solid rgb(55 65 81);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: border-color 0.3s ease;
}

.modern-footer-back-link:hover .modern-footer-back-icon {
    border-color: rgb(196 181 253);
}

.modern-footer-back-arrow {
    width: 0.75rem;
    height: 0.75rem;
    transition: transform 0.3s ease;
}

.modern-footer-back-link:hover .modern-footer-back-arrow {
    transform: translateY(-2px);
}

.modern-footer-back-text {
    transition: color 0.3s ease;
}

.modern-footer-back-link:hover .modern-footer-back-text {
    color: rgb(196 181 253);
}

.modern-footer-gradient-line {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(147 51 234 / 0.5), transparent);
}

/* ===== UTILITY CLASSES ===== */
.modern-hidden {
    display: none;
}

.modern-w-4 {
    width: 1rem;
}

.modern-h-4 {
    height: 1rem;
}

.modern-text-center {
    text-align: center;
}

.modern-text-white {
    color: #ffffff !important;
}

.modern-text-purple-300 {
    color: #c4b5fd !important;
}

.modern-text-orange-300 {
    color: #fdba74 !important;
}

.modern-text-cyan-300 {
    color: #67e8f9 !important;
}

.modern-text-green-300 {
    color: #86efac !important;
}

.modern-text-gray-300 {
    color: #d1d5db !important;
}

.modern-text-gray-400 {
    color: #9ca3af !important;
}

.modern-text-gray-500 {
    color: #6b7280 !important;
}

/* Ensure proper text rendering */
.modern-theme * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
