"use client";
import React from 'react';
import ContentEditable from 'react-contenteditable';
import { cn } from '@/lib/utils';

interface EditableTextProps {
    initialValue: string;
    isEditing: boolean;
    onSave: (value: string) => void;
    className?: string;
    tagName?: 'h1' | 'h2' | 'h3' | 'p' | 'div' | 'span';
}

export function EditableText({ initialValue, isEditing, onSave, className, tagName = 'div' }: EditableTextProps) {
    if (isEditing) {
        return (
            <ContentEditable
                html={initialValue || ''}
                onChange={() => { }} // We only save on blur
                onBlur={(e) => onSave(e.currentTarget.textContent || '')}
                tagName={tagName}
                className={cn(
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-md -m-1 p-1 cursor-text",
                    className
                )}
            />
        );
    }
    return React.createElement(tagName, { className }, initialValue);
}