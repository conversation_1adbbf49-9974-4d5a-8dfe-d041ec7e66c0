import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

// const buttonVariants = cva(
//   "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-backgroundPrimary",
//   {
//     variants: {
//       variant: {
//         // --- THIS IS THE PART WE ARE CHANGING ---

//         // The main call-to-action button
//         default:
//           "",

//         // The button for dangerous actions
//         destructive:
//           "",

//         // A button with a border and transparent background
//         outline:
//           "text-lg px-8 py-4 glass-effect hover:shadow-lg transition-all duration-300 rounded-lg font-semibold text-textPrimary",

//         // A subtler button with a light background
//         secondary:
//           "",

//         // A button with no background or border, just text
//         ghost:
//           "hover:bg-accent hover:text-textPrimary",

//         // A button that looks like a hyperlink
//         link: "text-accent underline-offset-4 hover:underline",

//         plain: "hover:text-accent bg-transparent",

//         cta: "text-lg px-8 py-4 bg-gradient-to-r from-brandPrimary to-brandSecondary hover:shadow-2xl hover:shadow-brandPrimary/25 transform hover:scale-105 transition-all duration-300 group rounded-lg font-semibold text-white flex items-center justify-center gap-2",

//         signIn: "px-6 py-2 bg-gradient-to-r from-brandPrimary to-brandSecondary hover:shadow-lg hover:shadow-brandPrimary/25 transform hover:scale-105 transition-all duration-200 rounded-lg font-semibold text-white"
//       },
//       size: {
//         default: "h-12 px-4 py-2",
//         sm: "h-9 rounded-md px-3",
//         lg: "h-16 rounded-md px-8",
//         icon: "h-10 w-10",
//       },
//     },
//     defaultVariants: {
//       variant: "default",
//       size: "default",
//     },
//   }
// )

// The rest of the component remains the same

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-backgroundPrimary",
  {
    variants: {
      variant: {
        // The main call-to-action button using your brand gradient
        default:
          "bg-gradient-to-r from-brandPrimary to-brandSecondary text-white hover:shadow-lg hover:shadow-brandPrimary/25 transform hover:scale-[1.02] transition-all duration-300",

        // Destructive action button using error colors
        destructive:
          "bg-error text-white hover:opacity-90 transform hover:scale-[1.02] transition-all duration-300 hover:shadow-lg hover:shadow-error/25",

        // Secondary button with subtle background
        secondary:
          "bg-backgroundSecondary text-textPrimary hover:bg-backgroundTertiary border border-borderPrimary transition-all duration-300",

        outline:
          "text-lg px-8 py-4 glass-effect hover:shadow-lg transition-all duration-300 rounded-lg font-semibold text-textPrimary",

        ghost:
          "bg-transparent text-textPrimary hover:text-textPrimary transition-colors duration-200 focus:ring-1 focus:ring-borderPrimary",

        link:
          "text-accent underline-offset-4 hover:underline",

        plain:
          "hover:text-accent bg-transparent",

        cta:
          "text-lg px-8 py-4 bg-gradient-to-r from-brandPrimary to-brandSecondary hover:shadow-2xl hover:shadow-brandPrimary/25 transform hover:scale-105 transition-all duration-300 group rounded-lg font-semibold text-white flex items-center justify-center gap-2",

        signIn:
          "px-6 py-2 bg-gradient-to-r from-brandPrimary to-brandSecondary hover:shadow-lg hover:shadow-brandPrimary/25 transform hover:scale-105 transition-all duration-200 rounded-lg font-semibold text-white"
      },
      size: {
        default: "h-12 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-16 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }