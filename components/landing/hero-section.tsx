"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "../ui/button";

export function HeroSection() {
  const router = useRouter();

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-backgroundPrimary via-backgroundSecondary to-backgroundTertiary pt-32 lg:pt-40 pb-20">
      {/* Background decorative elements - simplified and more stable */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-32 w-80 h-80 rounded-full bg-gradient-to-r from-brandPrimary/10 to-brandSecondary/10 blur-3xl opacity-70"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 rounded-full bg-gradient-to-r from-brandAccent/10 to-brandPrimary/10 blur-3xl opacity-70"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-brandSecondary/5 to-brandAccent/5 blur-3xl opacity-50"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          {/* Badge - simplified animation */}
          <div className="inline-flex items-center space-x-2 glass-effect rounded-full px-4 py-2 mb-8 opacity-0 animate-fade-in">
            <Sparkles className="w-4 h-4 text-brandAccent" />
            <span className="text-sm font-medium text-textSecondary">
              Build your professional presence in minutes
            </span>
          </div>

          {/* Main heading - staggered but cleaner animation */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6">
            <span
              className="gradient-text block opacity-0 animate-slide-up"
              style={{ animationDelay: "0.2s" }}
            >
              Create Stunning
            </span>
            <span
              className="text-textPrimary block opacity-0 animate-slide-up"
              style={{ animationDelay: "0.4s" }}
            >
              Portfolios That
            </span>
            <span
              className="gradient-text block opacity-0 animate-slide-up"
              style={{ animationDelay: "0.6s" }}
            >
              Get You Hired
            </span>
          </h1>

          {/* Subtitle */}
          <p
            className="text-lg sm:text-xl md:text-2xl text-textSecondary max-w-3xl mx-auto mb-12 leading-relaxed opacity-0 animate-slide-up"
            style={{ animationDelay: "0.8s" }}
          >
            Join thousands of professionals who&apos;ve landed their dream jobs with
            <span className="text-brandPrimary font-semibold">
              {" "}
              beautiful portfolios
            </span>
            ,
            <span className="text-brandSecondary font-semibold">
              {" "}
              professional resumes
            </span>
            , and
            <span className="text-brandAccent font-semibold">
              {" "}
              powerful personal branding
            </span>
            .
          </p>

          {/* CTA Buttons */}
          <div
            className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16 opacity-0 animate-slide-up"
            style={{ animationDelay: "1s" }}
          >
            <Button
              onClick={() => router.push("/login")}
              variant="cta"       >
              Start Building Free
              <ArrowRight className="h-5 w-5 text-white group-hover:translate-x-1 transition-transform duration-200" />
            </Button>
            <Button
              variant="outline"
              onClick={() =>
                document
                  .getElementById("features")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              See How It Works
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
