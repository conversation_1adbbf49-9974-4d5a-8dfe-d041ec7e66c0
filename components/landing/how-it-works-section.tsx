import { Card } from "@/components/ui/card";
import { Check<PERSON>ir<PERSON>, ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "../ui/button";

const steps = [
    {
        step: "01",
        title: "Choose Your Template",
        description:
            "Select from our collection of professionally designed templates that match your style and industry.",
        features: [
            "Minimalist designs",
            "Creative layouts",
            "Industry-specific options",
        ],
    },
    {
        step: "02",
        title: "Customize Your Content",
        description:
            "Use our intuitive editor to add your projects, experience, and personal information with real-time preview.",
        features: [
            "Drag & drop editor",
            "Real-time preview",
            "Rich text formatting",
        ],
    },
    {
        step: "03",
        title: "Publish & Share",
        description:
            "Get your custom URL and share your portfolio with employers, clients, or on social media.",
        features: ["Custom URL", "SEO optimized", "Mobile responsive"],
    },
];

export function HowItWorksSection() {
    return (
        <section id="how-it-works" className="py-20 lg:py-32 bg-backgroundPrimary">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                {/* Section header */}
                <div className="text-center mb-16">
                    <div className="inline-flex items-center space-x-2 glass-effect rounded-full px-4 py-2 mb-6">
                        <span className="text-sm font-medium text-brandSecondary">
                            How It Works
                        </span>
                    </div>
                    <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
                        <span className="text-textPrimary">Get Started in</span>
                        <br />
                        <span className="gradient-text">Three Simple Steps</span>
                    </h2>
                    <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
                        Building your professional portfolio has never been easier. Follow
                        these simple steps and you&apos;ll be live in minutes.
                    </p>
                </div>

                {/* Steps */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
                    {steps.map((step, index) => (
                        <div key={step.step} className="relative">
                            <Card className="glass-effect p-8 h-full border-borderPrimary hover:shadow-xl hover:shadow-brandPrimary/10 transition-all duration-500">
                                {/* Step number */}
                                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center mb-6 text-white font-bold text-xl">
                                    {step.step}
                                </div>

                                {/* Content */}
                                <h3 className="text-2xl font-bold text-textPrimary mb-4">
                                    {step.title}
                                </h3>
                                <p className="text-textSecondary leading-relaxed mb-6">
                                    {step.description}
                                </p>

                                {/* Features list */}
                                <ul className="space-y-3">
                                    {step.features.map((feature) => (
                                        <li key={feature} className="flex items-center space-x-3">
                                            <CheckCircle className="w-5 h-5 text-brandAccent flex-shrink-0" />
                                            <span className="text-textSecondary">{feature}</span>
                                        </li>
                                    ))}
                                </ul>
                            </Card>

                            {/* Arrow connector for desktop */}
                            {index < steps.length - 1 && (
                                <div className="hidden lg:block absolute top-20 -right-6 z-10">
                                    <div className="w-12 h-12 rounded-full bg-backgroundSecondary border border-borderPrimary flex items-center justify-center">
                                        <ArrowRight className="w-6 h-6 text-brandPrimary" />
                                    </div>
                                </div>
                            )}
                        </div>
                    ))}
                </div>

                {/* Bottom CTA */}
                <div className="text-center mt-16">
                    <div className="glass-effect rounded-2xl p-8 max-w-xl mx-auto border border-borderPrimary">
                        <h3 className="text-2xl font-bold gradient-text mb-4">
                            Ready to get started?
                        </h3>
                        <p className="text-textSecondary mb-6">
                            It takes less than 5 minutes to create your first portfolio.
                        </p>
                        <Button
                            variant="cta"
                            className="flex items-center justify-center mx-auto"
                        >
                            <span>Start Building Now</span>
                            <ArrowRight className="w-5 h-5" />
                        </Button>
                    </div>
                </div>
            </div>
        </section>
    );
}
