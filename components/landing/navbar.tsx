"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import Image from "next/image";


export function LandingNavbar() {
  const router = useRouter();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);




  // Add scroll spy effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);

    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks = [
    { href: "#home", label: "Home" },
    { href: "#features", label: "Features" },
    { href: "#how-it-works", label: "How it Works" },
  ];

  return (
    <header
      className={`fixed top-0 w-full right-0 z-50 transition-all duration-300 ${isScrolled
        ? "glass-effect border-b border-borderPrimary shadow-lg shadow-shadowPrimary"
        : "bg-transparent"
        }`}
    >
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center group gap-1">


            <Image src="/logo.png" alt=" Logo" width={24} height={24} />

            <span className="font-bold text-xl lg:text-2xl gradient-text">
              profolify
            </span>
          </Link>

          {/* Desktop Navigation */}

          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-textSecondary hover:text-brandPrimary transition-colors duration-200 font-medium"
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            <ThemeToggle />

            {/* Desktop CTA */}
            <div className="hidden sm:flex items-center">
              <Button
                onClick={() => router.push("/login")}
                variant="signIn"       >
                Sign In
              </Button>
            </div>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-borderPrimary mt-2 pt-4 pb-6 space-y-4 glass-effect rounded-b-lg">
            {navLinks.map((link) => (

              <Link
                key={link.href}
                href={link.href}
                className="block text-textSecondary hover:text-brandPrimary transition-colors duration-200 font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}
            <div className="flex flex-col space-y-2 pt-4">

              <Button
                onClick={() => {
                  router.push("/login");
                  setIsMobileMenuOpen(false);
                }} variant="signIn"       >
                Sign In
              </Button>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}