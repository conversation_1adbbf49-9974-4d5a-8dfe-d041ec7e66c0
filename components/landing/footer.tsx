import React from "react";
import Link from "next/link";
import { Github, Twitter, Linkedin, Mail, Heart } from "lucide-react";

export function LandingFooter() {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    product: [
      { label: "Features", href: "#features" },
      { label: "Templates", href: "#templates" },
      { label: "Pricing", href: "#pricing" },
      { label: "Examples", href: "#examples" },
    ],
    company: [
      { label: "About", href: "/about" },
      { label: "Blog", href: "/blog" },
      { label: "Careers", href: "/careers" },
      { label: "Contact", href: "/contact" },
    ],
    support: [
      { label: "Help Center", href: "/help" },
      { label: "Privacy Policy", href: "/privacy" },
      { label: "Terms of Service", href: "/terms" },
      { label: "Cookie Policy", href: "/cookies" },
    ],
  };

  const socialLinks = [
    { icon: Github, href: "https://github.com", label: "GitHub" },
    { icon: Twitter, href: "https://twitter.com", label: "Twitter" },
    { icon: Linkedin, href: "https://linkedin.com", label: "LinkedIn" },
    { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
  ];

  return (
    <footer className="bg-backgroundTertiary border-t border-borderPrimary">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main footer content */}
        <div className="py-16 lg:py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-8 lg:gap-12">
            {/* Brand section */}
            <div className="lg:col-span-4">
              <Link href="/" className="flex items-center space-x-2 mb-6 group">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center transform group-hover:scale-110 transition-transform duration-200">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
                <span className="font-bold text-2xl gradient-text">
                  Profolify
                </span>
              </Link>
              <p className="text-textSecondary leading-relaxed mb-6 max-w-sm">
                Create stunning portfolios and professional resumes that help
                you land your dream job. No coding required, just your
                creativity.
              </p>

              {/* Social links */}
              <div className="flex space-x-4">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon;
                  return (
                    <Link
                      key={social.label}
                      href={social.href}
                      className="w-10 h-10 rounded-lg bg-backgroundSecondary border border-borderPrimary flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:bg-brandPrimary/10 hover:border-brandPrimary/30 transition-all duration-200"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label={social.label}
                    >
                      <IconComponent className="w-5 h-5" />
                    </Link>
                  );
                })}
              </div>
            </div>

            {/* Links sections */}
            <div className="lg:col-span-8">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
                {/* Product */}
                <div>
                  <h3 className="text-textPrimary font-semibold text-lg mb-4">
                    Product
                  </h3>
                  <ul className="space-y-3">
                    {footerLinks.product.map((link) => (
                      <li key={link.label}>
                        <Link
                          href={link.href}
                          className="text-textSecondary hover:text-brandPrimary transition-colors duration-200"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Company */}
                <div>
                  <h3 className="text-textPrimary font-semibold text-lg mb-4">
                    Company
                  </h3>
                  <ul className="space-y-3">
                    {footerLinks.company.map((link) => (
                      <li key={link.label}>
                        <Link
                          href={link.href}
                          className="text-textSecondary hover:text-brandPrimary transition-colors duration-200"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Support */}
                <div>
                  <h3 className="text-textPrimary font-semibold text-lg mb-4">
                    Support
                  </h3>
                  <ul className="space-y-3">
                    {footerLinks.support.map((link) => (
                      <li key={link.label}>
                        <Link
                          href={link.href}
                          className="text-textSecondary hover:text-brandPrimary transition-colors duration-200"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="py-6 border-t border-borderPrimary">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4 text-sm text-textSecondary">
              <span>© {currentYear} Profolify. All rights reserved.</span>
              <span className="hidden sm:inline">•</span>
              <span className="flex items-center space-x-1">
                <span>Made with</span>
                <Heart className="w-4 h-4 text-red-500 fill-current" />
                <span>for professionals</span>
              </span>
            </div>

            <div className="flex items-center space-x-6 text-sm text-textSecondary">
              <span className="flex items-center space-x-2">
                <span>Built with</span>
                <span className="text-brandPrimary font-medium">Next.js</span>
                <span>•</span>
                <span className="text-brandSecondary font-medium">
                  Tailwind
                </span>
                <span>•</span>
                <span className="text-brandAccent font-medium">Firebase</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
