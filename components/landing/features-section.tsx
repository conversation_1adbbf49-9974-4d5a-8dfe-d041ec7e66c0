"use client";

import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Edit3,
  Share2,
  FileText,
  Palette,
  BarChart3,
  Smartphone,
} from "lucide-react";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";

const features = [
  {
    icon: Edit3,
    title: "Intuitive Builder",
    description:
      "Drag-and-drop editor with real-time preview. No coding required - just your creativity and content.",
    color: "from-brandPrimary to-brandSecondary",
    highlight: "No Code Required",
  },
  {
    icon: Palette,
    title: "Beautiful Templates",
    description:
      "Professionally designed templates that adapt perfectly to any device and look stunning in both light and dark themes.",
    color: "from-brandSecondary to-brandAccent",
    highlight: "Professional Design",
  },
  {
    icon: FileText,
    title: "LaTeX Resume Export",
    description:
      "Generate pixel-perfect PDF resumes with professional LaTeX formatting in seconds. Stand out with superior typography.",
    color: "from-brandAccent to-brandPrimary",
    highlight: "PDF Export",
  },
  {
    icon: Share2,
    title: "Custom URLs",
    description:
      "Get a clean, memorable link to share your portfolio with recruiters, clients, and on social media platforms.",
    color: "from-brandPrimary to-brandAccent",
    highlight: "Easy Sharing",
  },
  {
    icon: Smartphone,
    title: "Mobile Optimized",
    description:
      "Your portfolio automatically adapts and looks perfect on every device - desktop, tablet, and mobile.",
    color: "from-brandSecondary to-brandPrimary",
    highlight: "Responsive Design",
  },
  {
    icon: BarChart3,
    title: "Analytics Insights",
    description:
      "Track portfolio views, visitor engagement, and see which sections get the most attention from potential employers.",
    color: "from-brandAccent to-brandSecondary",
    highlight: "Track Performance",
  },
];

export function FeaturesSection() {

  const router = useRouter();


  return (
    <section id="features" className="py-24 lg:py-32 bg-backgroundSecondary">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 glass-effect rounded-full px-6 py-3 mb-8">
            <span className="text-sm font-semibold text-brandPrimary">
              ✨ Features
            </span>
          </div>
          <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-8 leading-tight">
            <span className="gradient-text">Everything You Need</span>
            <br />
            <span className="text-textPrimary">To Stand Out</span>
          </h2>
          <p className="text-xl md:text-2xl text-textSecondary max-w-4xl mx-auto leading-relaxed">
            Powerful features designed to help you create a professional
            presence that gets noticed by top employers and clients worldwide.
          </p>
        </div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10 mb-20">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <Card
                key={feature.title}
                className="group relative overflow-hidden glass-effect hover:shadow-2xl hover:shadow-brandPrimary/15 transition-all duration-500 hover:scale-[1.02] border-borderPrimary bg-backgroundPrimary/80"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Gradient overlay */}
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-[0.03] transition-opacity duration-500`}
                ></div>

                {/* Highlight badge */}
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span className="text-xs font-semibold text-brandPrimary bg-brandPrimary/10 px-2 py-1 rounded-full">
                    {feature.highlight}
                  </span>
                </div>

                <CardHeader className="relative p-8">
                  {/* Icon with enhanced styling */}
                  <div
                    className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} p-4 mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg`}
                  >
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <CardTitle className="text-2xl font-bold text-textPrimary mb-4 group-hover:text-brandPrimary transition-colors duration-300">
                    {feature.title}
                  </CardTitle>
                  <CardDescription className="text-textSecondary leading-relaxed text-lg group-hover:text-textPrimary transition-colors duration-300">
                    {feature.description}
                  </CardDescription>
                </CardHeader>

                {/* Bottom accent line */}
                <div
                  className={`h-1 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left`}
                ></div>
              </Card>
            );
          })}
        </div>

        {/* Bottom CTA section */}
        <div className="text-center">
          <div className="glass-effect rounded-3xl p-12 max-w-4xl mx-auto border border-borderPrimary bg-backgroundPrimary/60">
            <div className="mb-8">
              <h3 className="text-3xl md:text-4xl font-bold gradient-text mb-6">
                Ready to build your portfolio?
              </h3>
              <p className="text-xl text-textSecondary max-w-2xl mx-auto leading-relaxed">
                Join thousands of professionals who&apos;ve already created stunning
                portfolios and landed their dream jobs.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button
                onClick={() => router.push("/login")}
                variant="cta"       >

                Start Building Now

              </Button>
              <Button
                variant="outline"
                onClick={() =>
                  document
                    .getElementById("how-it-works")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
                className="px-10 py-4 glass-effect text-textPrimary rounded-xl font-semibold text-lg hover:shadow-xl transition-all duration-300 border border-borderPrimary"
              >
                See How It Works
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 mt-12 pt-8 border-t border-borderPrimary">
              <div className="text-center">
                <div className="text-3xl font-bold gradient-text mb-2">
                  5 min
                </div>
                <div className="text-textSecondary">Setup Time</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold gradient-text mb-2">
                  100%
                </div>
                <div className="text-textSecondary">Free Forever</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold gradient-text mb-2">
                  24/7
                </div>
                <div className="text-textSecondary">Support</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}