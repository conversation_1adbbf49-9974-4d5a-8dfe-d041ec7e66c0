"use client";
import Image from "next/image";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "@/components/portfolio-themes/types";
import { Upload, Download, FileText, Loader2 } from "lucide-react";
import { PortfolioData } from "@/lib/types";
import { PortfolioImage } from "../../ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";

export function HeroSection({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isExport = useIsExport();
    const isUploadingProfile = isEditing && context?.state.isUploading?.type === 'profile';
    const isUploadingResume = isEditing && context?.state.isUploading?.type === 'resume';

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section className="relative min-h-screen flex flex-col items-center justify-center text-center px-4 sm:px-6 lg:px-8 py-20 bg-gradient-to-br from-background via-background to-muted/20 overflow-hidden">
            {/* Background decoration */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute -top-60 -right-50 w-80 h-80 bg-white rounded-full blur-3xl"></div>
                <div className="absolute -bottom-60 -left-50 w-80 h-80 bg-white rounded-full blur-3xl"></div>
            </div>

            <div className="relative z-10 max-w-4xl mx-auto">
                {/* Profile Image */}
                <div className="relative w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 mx-auto mb-8 group">
                    {/* Loading overlay - shows when uploading */}
                    {isUploadingProfile && (
                        <div className="absolute inset-0 bg-black/90 rounded-full flex items-center justify-center z-30 backdrop-blur-sm">
                            <div className="flex flex-col items-center gap-3 text-white">
                                <div className="relative">
                                    <div className="w-12 h-12 border-4 border-primary/30 rounded-full"></div>
                                    <div className="absolute inset-0 w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                                </div>
                                <div className="text-center">
                                    <p className="text-sm font-semibold">Uploading</p>
                                    <p className="text-xs text-gray-300">Please wait...</p>
                                </div>
                            </div>
                        </div>
                    )}

                    <PortfolioImage
                        key={`profile-${data.profileImageUrl || 'placeholder'}-${isUploadingProfile ? 'uploading' : 'ready'}`}
                        width={192} height={192} priority
                        src={data.profileImageUrl || 'https://placehold.co/192x192/e2e8f0/64748b?text=Me'}
                        alt={data.userName}
                        className={`w-full h-full rounded-full object-cover border-4 border-white shadow-2xl ring-4 ring-primary/10 transition-all duration-300 group-hover:scale-105 ${isUploadingProfile ? 'opacity-30' : 'opacity-100'}`}
                    />
                    {isEditing && !isUploadingProfile && (
                        <label htmlFor="profile-upload" className="absolute inset-0 bg-black/60 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-all duration-300 cursor-pointer z-20">
                            <div className="flex flex-col items-center gap-1">
                                <Upload className="h-5 w-5" />
                                <span className="text-xs font-medium">Change</span>
                            </div>
                            <input
                                id="profile-upload"
                                type="file"
                                className="hidden"
                                accept="image/*"
                                onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'profile' })}
                                disabled={isUploadingProfile}
                            />
                        </label>
                    )}
                </div>

                {/* Name and Title */}
                <div className="space-y-4 mb-12">
                    <EditableText
                        isEditing={isEditing}
                        tagName="h1"
                        className={`text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight ${
                            isExport
                                ? 'text-foreground'
                                : 'bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent'
                        }`}
                        initialValue={data.userName}
                        onSave={(val) => handleUpdate('userName', val)}
                    />
                    <EditableText
                        isEditing={isEditing}
                        tagName="p"
                        className="text-lg sm:text-xl lg:text-2xl text-muted-foreground font-medium tracking-wide max-w-2xl mx-auto"
                        initialValue={data.profession}
                        onSave={(val) => handleUpdate('profession', val)}
                    />
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
                    {isEditing && (
                        <Button asChild variant="outline" size="lg" className="min-w-[160px] h-12 border-2 hover:bg-primary hover:text-primary-foreground transition-all duration-300">
                            <label className="flex items-center cursor-pointer">
                                {isUploadingResume ? (
                                    <Loader2 className="animate-spin mr-2 h-4 w-4" />
                                ) : (
                                    <FileText className="mr-2 h-4 w-4" />
                                )}
                                Upload Resume
                                <input id="resume-upload" type="file" className="hidden" accept=".pdf" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'resume' })} />
                            </label>
                        </Button>
                    )}
                    {data.resumeUrl && (
                        <Button asChild size="lg" className="min-w-[160px] h-12 bg-primary hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <a href={data.resumeUrl} target="_blank" rel="noopener noreferrer">
                                <Download className="mr-2 h-4 w-4" />
                                Download Resume
                            </a>
                        </Button>
                    )}
                </div>

                {/* Scroll indicator */}
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                    <div className="w-6 h-10 border-2 border-muted-foreground/30 rounded-full flex justify-center">
                        <div className="w-1 h-3 bg-muted-foreground/30 rounded-full mt-2 animate-pulse"></div>
                    </div>
                </div>
            </div>
        </section>
    );
}