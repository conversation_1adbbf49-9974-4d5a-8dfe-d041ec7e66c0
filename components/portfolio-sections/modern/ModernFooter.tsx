"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Heart, Code, Sparkles } from "lucide-react";

export function ModernFooter({ isEditing, serverData }: SectionProps) {
    const data = isEditing ? useEditor().state.formData : serverData!;
    const currentYear = new Date().getFullYear();

    return (
        <footer className="modern-footer">
            {/* Background decoration */}
            <div className="modern-footer-background">
                <div className="modern-footer-glow-left"></div>
                <div className="modern-footer-glow-right"></div>
            </div>

            <div className="modern-footer-content">
                <div className="modern-footer-inner">
                    {/* Main content */}
                    <div className="modern-footer-main">
                        <h3 className="modern-footer-title modern-gradient-text-primary">
                            {data.userName || "Portfolio"}
                        </h3>

                        <p className="modern-footer-subtitle modern-gradient-text-secondary">
                            {data.profession || "Creative Professional"}
                        </p>
                    </div>

                    {/* Divider */}
                    <div className="modern-footer-divider"></div>

                    {/* Copyright and powered by */}
                    <div className="modern-footer-bottom">
                        <p className="modern-footer-text">
                            © {currentYear} {data.userName || "Portfolio"}. All rights reserved.
                        </p>

                        {/* Powered by section */}
                        <div className="modern-footer-brand">
                            <span>Built with</span>
                            <Heart className="modern-footer-heart" />
                            <span>on</span>
                            <div className="modern-footer-brand-icons">
                                <Code className="modern-footer-code-icon" />
                                <span className="modern-footer-brand-name modern-gradient-text-accent">
                                    Profolify
                                </span>
                                <Sparkles className="modern-footer-sparkles-icon" />
                            </div>
                        </div>
                    </div>

                    {/* Back to top hint */}
                    <div className="modern-footer-back-to-top">
                        <a
                            href="#"
                            className="modern-footer-back-link"
                        >
                            <div className="modern-footer-back-icon">
                                <svg className="modern-footer-back-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                                </svg>
                            </div>
                            <span className="modern-footer-back-text">Back to top</span>
                        </a>
                    </div>
                </div>
            </div>

            {/* Bottom gradient line */}
            <div className="modern-footer-gradient-line"></div>
        </footer>
    );
}
