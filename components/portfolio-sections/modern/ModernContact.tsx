"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { Gith<PERSON>, Linkedin, Twitter, ArrowRight } from "lucide-react";
import { PortfolioData } from "@/lib/types";
import { useIsExport } from "@/contexts/ExportContext";

export function ModernContact({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isExport = useIsExport();

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    const handleSocialUpdate = (platform: string, value: string) => {
        if (dispatch) {
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'socials' as any,
                    value: { ...data.socials, [platform]: value }
                }
            });
        }
    };

    return (
        <section id="contact" className="modern-section modern-contact-section">
            {/* Background elements */}
            <div className="modern-section-background"></div>
            <div className="modern-contact-grid-bg"></div>
            <div className="modern-contact-glow"></div>

            <div className="modern-container modern-text-center">
                {/* Section header */}
                <div className="modern-section-header">
                    <h2 className={`modern-section-title ${
                        isExport
                            ? 'modern-text-white'
                            : 'modern-gradient-text-primary'
                    }`}>
                        Let&#39;s Build Something
                    </h2>
                    <h2 className={`modern-contact-subtitle ${
                        isExport
                            ? 'modern-text-purple-300'
                            : 'modern-gradient-text-secondary'
                    }`}>
                        Amazing Together
                    </h2>
                    <p className="modern-section-subtitle">
                        Ready to turn your vision into reality? I'm currently available for exciting projects and collaborations. Let's create something extraordinary.
                    </p>
                    <div className="modern-section-divider"></div>
                </div>

                {/* Contact card */}
                <div className="modern-contact-card-container">
                    <div className="modern-contact-card">
                        <div className="modern-contact-content">
                            {/* Email section */}
                            <div className="modern-contact-email-section">
                                <h3 className="modern-contact-section-title">Drop me a line</h3>
                                <a
                                    href={`mailto:${data.contactEmail}`}
                                    className={`modern-contact-email-link ${
                                        isExport
                                            ? 'modern-text-purple-300'
                                            : 'modern-gradient-text-accent'
                                    }`}
                                >
                                    <EditableText
                                        isEditing={isEditing}
                                        tagName="span"
                                        className={isExport
                                            ? 'modern-text-purple-300'
                                            : 'modern-gradient-text-accent'
                                        }
                                        initialValue={data.contactEmail || "<EMAIL>"}
                                        onSave={(val) => handleUpdate('contactEmail', val)}
                                    />
                                    <ArrowRight className="modern-contact-email-arrow" />
                                </a>
                            </div>

                            {/* Divider */}
                            <div className="modern-contact-divider"></div>

                            {/* Social links */}
                            <div className="modern-contact-social-section">
                                <h3 className="modern-contact-section-title">Connect with me</h3>

                                {isEditing ? (
                                    <div className="modern-contact-social-edit">
                                        <div className="modern-contact-social-field">
                                            <label className="modern-contact-social-label">
                                                <Github className="modern-w-4 modern-h-4" />
                                                GitHub URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.github || "https://github.com/username"}
                                                onSave={v => handleSocialUpdate('github', v)}
                                                className="modern-contact-social-input"
                                            />
                                        </div>
                                        <div className="modern-contact-social-field">
                                            <label className="modern-contact-social-label">
                                                <Linkedin className="modern-w-4 modern-h-4" />
                                                LinkedIn URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.linkedin || "https://linkedin.com/in/username"}
                                                onSave={v => handleSocialUpdate('linkedin', v)}
                                                className="modern-contact-social-input"
                                            />
                                        </div>
                                        <div className="modern-contact-social-field">
                                            <label className="modern-contact-social-label">
                                                <Twitter className="modern-w-4 modern-h-4" />
                                                Twitter URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.twitter || "https://twitter.com/username"}
                                                onSave={v => handleSocialUpdate('twitter', v)}
                                                className="modern-contact-social-input"
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className="modern-contact-social-links">
                                        {data.socials?.github && (
                                            <a
                                                href={data.socials.github}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="Github"
                                                className="modern-contact-social-link"
                                            >
                                                <div className="modern-contact-social-icon modern-contact-social-github">
                                                    <Github className="modern-contact-social-icon-svg" />
                                                </div>
                                                <span className="modern-contact-social-text">GitHub</span>
                                            </a>
                                        )}
                                        {data.socials?.linkedin && (
                                            <a
                                                href={data.socials.linkedin}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="LinkedIn"
                                                className="modern-contact-social-link"
                                            >
                                                <div className="modern-contact-social-icon modern-contact-social-linkedin">
                                                    <Linkedin className="modern-contact-social-icon-svg" />
                                                </div>
                                                <span className="modern-contact-social-text">LinkedIn</span>
                                            </a>
                                        )}
                                        {data.socials?.twitter && (
                                            <a
                                                href={data.socials.twitter}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="Twitter"
                                                className="modern-contact-social-link"
                                            >
                                                <div className="modern-contact-social-icon modern-contact-social-twitter">
                                                    <Twitter className="modern-contact-social-icon-svg" />
                                                </div>
                                                <span className="modern-contact-social-text">Twitter</span>
                                            </a>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Call to action */}
                <div className="modern-contact-cta">
                    <p className="modern-contact-cta-text">
                        Ready to start your project?
                    </p>
                    <div className="modern-contact-status">
                        <div className="modern-contact-status-dot modern-contact-status-dot-1"></div>
                        <span className="modern-contact-status-text">Available for new opportunities</span>
                        <div className="modern-contact-status-dot modern-contact-status-dot-2"></div>
                    </div>
                </div>
            </div>
        </section>
    );
}