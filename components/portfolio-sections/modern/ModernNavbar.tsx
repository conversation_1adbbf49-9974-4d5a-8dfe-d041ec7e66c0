// Modern Navbar with custom CSS styling for consistent export compatibility
"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>rigger, SheetTitle } from "@/components/ui/sheet";
import { Menu } from "lucide-react";

const navLinks = [
    { href: '#about', label: 'About' },
    { href: '#projects', label: 'Work' },
    { href: '#contact', label: 'Contact' },
];

export function ModernNavbar({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;

    const NavContent = () => (
        <>
            {navLinks.map(link => (
                <a key={link.href} href={link.href} className="modern-navbar-link">
                    {link.label}
                </a>
            ))}
        </>
    );

    return (
        <header className="modern-navbar">
            <div className="modern-navbar-container">
                <a href="#" className="modern-navbar-brand">
                    {data.userName || "Portfolio"}
                </a>

                <nav className="modern-navbar-nav">
                    <NavContent />
                </nav>

                {/* Mobile Menu */}
                <div className="modern-mobile-menu-button">
                    <Sheet>
                        <SheetTrigger asChild>
                            <button
                                type="button"
                                data-menu-button
                                className="modern-mobile-menu-button"
                                aria-label="Open navigation menu"
                                title="Open navigation menu"
                            >
                                <Menu className="modern-w-4 modern-h-4" />
                            </button>
                        </SheetTrigger>
                        <SheetContent side="right" className="modern-sheet-content">
                            <SheetTitle className="modern-hidden">Navigation Menu</SheetTitle>
                            <nav className="modern-mobile-menu">
                                {navLinks.map(link => (
                                    <a key={link.href} href={link.href} className="modern-navbar-link modern-hover-text-purple-400">
                                        {link.label}
                                    </a>
                                ))}
                            </nav>
                        </SheetContent>
                    </Sheet>
                </div>
            </div>

            {/* Static Export Mobile Menu Panel */}
            <div data-menu-panel className="modern-hidden modern-md-hidden modern-mobile-menu-panel">
                <nav className="modern-mobile-menu-nav">
                    {navLinks.map(link => (
                        <a
                            key={link.href}
                            href={link.href}
                            data-menu-link
                            className="modern-mobile-menu-link"
                        >
                            {link.label}
                        </a>
                    ))}
                </nav>
            </div>
        </header>
    );
};
