"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { useIsExport } from "@/contexts/ExportContext";

export function ModernAbout({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isExport = useIsExport();

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section id="about" className="relative px-4 sm:px-6 lg:px-8 py-20 lg:py-32 bg-black text-white overflow-hidden">
            {/* Background elements */}
            <div className="absolute inset-0">
                {/* Subtle grid */}
                <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:100px_100px]"></div>

                {/* Floating orbs */}
                <div className="absolute top-40 right-20 w-64 h-64 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"></div>
                <div className="absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl"></div>
            </div>

            <div className="container mx-auto px-4 relative z-10">
                <div className="max-w-6xl mx-auto">
                    {/* Section header */}
                    <div className="text-center mb-16">
                        <div className="inline-block">
                            <h2 className={`text-4xl sm:text-5xl lg:text-6xl font-black mb-4 ${
                                isExport
                                    ? 'text-white'
                                    : 'bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent'
                            }`}>
                                About Me
                            </h2>
                            <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto rounded-full"></div>
                        </div>
                    </div>

                    {/* Content grid */}
                    <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
                        {/* Text content */}
                        <div className="space-y-8">
                            <div className="relative">
                                {/* Quote mark decoration */}
                                <div className="absolute -top-4 -left-4 text-6xl text-white font-serif">&quot;</div>
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="div"
                                    className="text-lg lg:text-xl text-gray-300 leading-relaxed relative z-10"
                                    initialValue={data.about || "I'm a passionate professional dedicated to creating exceptional experiences. My journey has been shaped by curiosity, creativity, and a commitment to excellence. I believe in the power of innovation to solve complex problems and create meaningful impact."}
                                    onSave={(val) => handleUpdate('about', val)}
                                />
                            </div>
                        </div>

                        {/* Stats/Skills section */}
                        <div className="space-y-8">
                            <div className="grid grid-cols-2 gap-6">
                                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 text-center hover:border-purple-500/50 transition-colors duration-300">
                                    <div className={`text-3xl font-bold mb-2 ${
                                        isExport
                                            ? 'text-purple-300'
                                            : 'bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent'
                                    }`}>
                                        5+
                                    </div>
                                    <div className="text-sm text-gray-400 uppercase tracking-wider">
                                        Years Experience
                                    </div>
                                </div>

                                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 text-center hover:border-cyan-500/50 transition-colors duration-300">
                                    <div className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-2">
                                        50+
                                    </div>
                                    <div className="text-sm text-gray-400 uppercase tracking-wider">
                                        Projects Completed
                                    </div>
                                </div>

                                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 text-center hover:border-green-500/50 transition-colors duration-300">
                                    <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-2">
                                        100%
                                    </div>
                                    <div className="text-sm text-gray-400 uppercase tracking-wider">
                                        Client Satisfaction
                                    </div>
                                </div>

                                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 text-center hover:border-orange-500/50 transition-colors duration-300">
                                    <div className={`text-3xl font-bold mb-2 ${
                                        isExport
                                            ? 'text-orange-300'
                                            : 'bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent'
                                    }`}>
                                        24/7
                                    </div>
                                    <div className="text-sm text-gray-400 uppercase tracking-wider">
                                        Dedication
                                    </div>
                                </div>
                            </div>

                            {/* Skills or values */}
                            <div className="space-y-4">
                                <h3 className="text-xl font-semibold text-gray-200 mb-4">Core Values</h3>
                                <div className="space-y-3">
                                    {['Innovation', 'Quality', 'Collaboration', 'Growth'].map((value) => (
                                        <div key={value} className="flex items-center gap-3">
                                            <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                                            <span className="text-gray-300">{value}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}