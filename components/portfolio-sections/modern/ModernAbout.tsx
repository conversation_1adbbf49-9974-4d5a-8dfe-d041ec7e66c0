"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { useIsExport } from "@/contexts/ExportContext";

export function ModernAbout({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isExport = useIsExport();

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section id="about" className="modern-section">
            {/* Background elements */}
            <div className="modern-section-background"></div>
            <div className="modern-section-grid"></div>

            <div className="modern-container">
                <div className="modern-section-header">
                    <h2 className={`modern-section-title ${
                        isExport
                            ? 'modern-text-white'
                            : 'modern-gradient-text-primary'
                    }`}>
                        About Me
                    </h2>
                    <div className="modern-section-divider"></div>
                </div>

                {/* Content grid */}
                <div className="modern-about-content">
                    {/* Text content */}
                    <div className="modern-about-text">
                        <div className="modern-about-quote-container">
                            {/* Quote mark decoration */}
                            <div className="modern-about-quote-mark">&quot;</div>
                            <EditableText
                                isEditing={isEditing}
                                tagName="div"
                                className="modern-about-description"
                                initialValue={data.about || "I'm a passionate professional dedicated to creating exceptional experiences. My journey has been shaped by curiosity, creativity, and a commitment to excellence. I believe in the power of innovation to solve complex problems and create meaningful impact."}
                                onSave={(val) => handleUpdate('about', val)}
                            />
                        </div>
                    </div>

                    {/* Stats/Skills section */}
                    <div className="modern-about-stats">
                        <div className="modern-about-stats-grid">
                            <div className="modern-about-stat-card modern-about-stat-purple">
                                <div className={`modern-about-stat-number ${
                                    isExport
                                        ? 'modern-text-purple-300'
                                        : 'modern-gradient-text-accent'
                                }`}>
                                    5+
                                </div>
                                <div className="modern-about-stat-label">
                                    Years Experience
                                </div>
                            </div>

                            <div className="modern-about-stat-card modern-about-stat-cyan">
                                <div className="modern-about-stat-number modern-gradient-text-cyan">
                                    50+
                                </div>
                                <div className="modern-about-stat-label">
                                    Projects Completed
                                </div>
                            </div>

                            <div className="modern-about-stat-card modern-about-stat-green">
                                <div className="modern-about-stat-number modern-gradient-text-green">
                                    100%
                                </div>
                                <div className="modern-about-stat-label">
                                    Client Satisfaction
                                </div>
                            </div>

                            <div className="modern-about-stat-card modern-about-stat-orange">
                                <div className={`modern-about-stat-number ${
                                    isExport
                                        ? 'modern-text-orange-300'
                                        : 'modern-gradient-text-orange'
                                }`}>
                                    24/7
                                </div>
                                <div className="modern-about-stat-label">
                                    Dedication
                                </div>
                            </div>
                        </div>

                        {/* Skills or values */}
                        <div className="modern-about-values">
                            <h3 className="modern-about-values-title">Core Values</h3>
                            <div className="modern-about-values-list">
                                {['Innovation', 'Quality', 'Collaboration', 'Growth'].map((value) => (
                                    <div key={value} className="modern-about-value-item">
                                        <div className="modern-about-value-dot"></div>
                                        <span className="modern-about-value-text">{value}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}