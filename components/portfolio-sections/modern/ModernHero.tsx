"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Upload, Loader2, Download, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";

export function ModernHero({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isUploadingProfile = isEditing && context!.state.isUploading?.type === 'profile';
    const isUploadingResume = isEditing && context!.state.isUploading?.type === 'resume';
    const isExport = useIsExport();

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section className="modern-hero">
            {/* Background elements */}
            <div className="modern-hero-background"></div>
            <div className="modern-hero-grid"></div>

            <div className="modern-hero-container">
                {/* Profile Image */}
                <div className="modern-hero-profile">
                    <div className="modern-hero-profile-container">
                        {/* Glowing ring */}
                        <div className="modern-hero-profile-glow"></div>
                        <div className="modern-hero-profile-inner"></div>

                        {/* Loading overlay - shows when uploading */}
                        {isUploadingProfile && (
                            <div className="modern-loading-overlay">
                                <div className="modern-loading-content">
                                    <div className="modern-loading-spinner">
                                        <div className="modern-loading-spinner-ring"></div>
                                        <div className="modern-loading-spinner-active"></div>
                                    </div>
                                    <div className="modern-loading-text">
                                        <p className={`modern-loading-title ${
                                            isExport
                                                ? 'modern-text-purple-300'
                                                : 'modern-gradient-text-accent'
                                        }`}>Uploading</p>
                                        <p className="modern-loading-subtitle">Please wait...</p>
                                    </div>
                                </div>
                            </div>
                        )}

                        <PortfolioImage
                            key={`modern-profile-${data.profileImageUrl || 'placeholder'}-${isUploadingProfile ? 'uploading' : 'ready'}`}
                            width={320} height={320} priority
                            src={data.profileImageUrl || 'https://placehold.co/320x320/1f2937/9ca3af?text=Profile'}
                            alt={data.userName}
                            className={`modern-hero-profile-image ${isUploadingProfile ? 'opacity-30' : 'opacity-100'}`}
                        />

                        {isEditing && !isUploadingProfile && (
                            <label htmlFor="profile-upload-modern" className="modern-upload-overlay">
                                <div className="modern-upload-content">
                                    <Upload className="modern-upload-icon" />
                                    <span className="modern-upload-text">Change Photo</span>
                                </div>
                                <input
                                    id="profile-upload-modern"
                                    type="file"
                                    className="modern-hidden"
                                    accept="image/*"
                                    onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'profile' })}
                                    disabled={isUploadingProfile}
                                />
                            </label>
                        )}
                    </div>
                </div>

                {/* Content */}
                <div className="modern-hero-content">
                    {/* Name with gradient */}
                    <EditableText
                        isEditing={isEditing}
                        tagName="h1"
                        className={`modern-hero-title ${
                            isExport
                                ? 'modern-text-white'
                                : 'modern-gradient-text-primary'
                        }`}
                        initialValue={data.userName}
                        onSave={(v) => handleUpdate('userName', v)}
                    />

                    {/* Profession with accent */}
                    <EditableText
                        isEditing={isEditing}
                        tagName="p"
                        className={`modern-hero-subtitle ${
                            isExport
                                ? 'modern-text-purple-300'
                                : 'modern-gradient-text-secondary'
                        }`}
                        initialValue={data.profession}
                        onSave={(v) => handleUpdate('profession', v)}
                    />

                    {/* Action buttons */}
                    <div className="modern-hero-buttons">
                        {isEditing && (
                            <Button asChild variant="outline" size="lg" className="modern-button-secondary">
                                <label className="modern-button-content">
                                    {isUploadingResume ? (
                                        <Loader2 className={`modern-w-4 modern-h-4 modern-button-icon-spinning`} />
                                    ) : (
                                        <FileText className="modern-w-4 modern-h-4 modern-button-icon" />
                                    )}
                                    Upload Resume
                                    <input id="resume-upload-modern" type="file" className="modern-hidden" accept=".pdf" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'resume' })} />
                                </label>
                            </Button>
                        )}
                        {data.resumeUrl && (
                            <Button asChild size="lg" className={isExport ? 'modern-button-secondary' : 'modern-button-primary'}>
                                <a href={data.resumeUrl} target="_blank" rel="noopener noreferrer" className="modern-button-content">
                                    <Download className="modern-w-4 modern-h-4 modern-button-icon" />
                                    Download Resume
                                </a>
                            </Button>
                        )}
                    </div>

                    {/* Scroll indicator */}
                    <div className="modern-hero-scroll-indicator">
                        <div className="modern-hero-scroll-mouse">
                            <div className="modern-hero-scroll-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};