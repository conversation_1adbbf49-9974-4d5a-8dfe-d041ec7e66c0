"use client";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Upload, Trash, ExternalLink } from "lucide-react";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";

export function ModernProjects({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isExport = useIsExport();

    const handleUpdate = (index: number, field: string, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_PROJECT', payload: { index, field: field as any, value } });
    };

    return (
        <section id="projects" className="modern-section modern-projects-section">
            {/* Background elements */}
            <div className="modern-section-background"></div>
            <div className="modern-projects-grid-bg"></div>

            <div className="modern-container">
                {/* Section header */}
                <div className="modern-section-header">
                    <h2 className={`modern-section-title ${
                        isExport
                            ? 'modern-text-white'
                            : 'modern-gradient-text-primary'
                    }`}>
                        Featured Work
                    </h2>
                    <p className="modern-section-subtitle">
                        Showcasing projects that demonstrate creativity, technical expertise, and innovative solutions
                    </p>
                    <div className="modern-section-divider"></div>
                </div>

                {/* Projects grid */}
                <div className="modern-projects-container">
                    {(data.projects || []).map((project, index) => {
                        const isUploading = isEditing && context!.state.isUploading?.type === 'project' && context!.state.isUploading?.id === project.id;
                        const isEven = index % 2 === 0;

                        return (
                            <div key={project.id} className="modern-project-item">
                                <div className={`modern-project-grid ${!isEven ? 'modern-project-grid-reverse' : ''}`}>
                                    {/* Project Image */}
                                    <div className={`modern-project-image-container ${!isEven ? 'modern-project-image-reverse' : ''}`}>
                                        <div className="modern-project-image-wrapper">
                                            {/* Glowing border effect */}
                                            <div className="modern-project-image-glow"></div>

                                            {/* Loading overlay - shows when uploading */}
                                            {isUploading && (
                                                <div className="modern-loading-overlay modern-project-loading">
                                                    <div className="modern-loading-content">
                                                        <div className="modern-loading-spinner">
                                                            <div className="modern-loading-spinner-ring"></div>
                                                            <div className="modern-loading-spinner-active"></div>
                                                        </div>
                                                        <div className="modern-loading-text">
                                                            <p className="modern-loading-title modern-gradient-text-accent">Uploading Image</p>
                                                            <p className="modern-loading-subtitle">Please wait...</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="modern-project-image-inner">
                                                <PortfolioImage
                                                    key={`modern-project-${project.id}-${project.imageUrl || 'placeholder'}-${isUploading ? 'uploading' : 'ready'}`}
                                                    src={project.imageUrl || 'https://placehold.co/800x600/1f2937/9ca3af?text=Project'}
                                                    alt={project.title}
                                                    fill
                                                    className={`modern-project-image ${isUploading ? 'opacity-30' : 'opacity-100'}`}
                                                />

                                                {/* Overlay gradient */}
                                                <div className="modern-project-image-overlay"></div>
                                            </div>

                                            {isEditing && !isUploading && (
                                                <label htmlFor={`project-image-${project.id}`} className="modern-upload-overlay modern-project-upload">
                                                    <div className="modern-upload-content">
                                                        <Upload className="modern-upload-icon" />
                                                        <span className="modern-upload-text">Change Image</span>
                                                    </div>
                                                    <input
                                                        key={`modern-project-input-${project.id}`}
                                                        id={`project-image-${project.id}`}
                                                        type="file"
                                                        className="modern-hidden"
                                                        accept="image/*"
                                                        onChange={(e) => {
                                                            if (e.target.files) {
                                                                onImageUpload!({ file: e.target.files[0], type: 'project', id: project.id });
                                                                // Reset the input value to allow re-uploading the same file
                                                                e.target.value = '';
                                                            }
                                                        }}
                                                        disabled={isUploading}
                                                    />
                                                </label>
                                            )}
                                        </div>
                                    </div>

                                    {/* Project Content */}
                                    <div className={`modern-project-content ${!isEven ? 'modern-project-content-reverse' : ''}`}>
                                        <div className="modern-project-header">
                                            <div className="modern-project-number">
                                                <div className="modern-project-number-line"></div>
                                                <span className="modern-project-number-text">
                                                    Project {String(index + 1).padStart(2, '0')}
                                                </span>
                                            </div>

                                            <EditableText
                                                isEditing={isEditing}
                                                tagName="h3"
                                                className={`modern-project-title ${
                                                    isExport
                                                        ? 'modern-text-white'
                                                        : 'modern-gradient-text-primary'
                                                }`}
                                                initialValue={project.title}
                                                onSave={(v) => handleUpdate(index, 'title', v)}
                                            />
                                        </div>

                                        <EditableText
                                            isEditing={isEditing}
                                            tagName="div"
                                            className="modern-project-description"
                                            initialValue={project.description}
                                            onSave={(v) => handleUpdate(index, 'description', v)}
                                        />

                                        <div className="modern-project-actions">
                                            {isEditing ? (
                                                <div className="modern-project-url-edit">
                                                    <label className="modern-project-url-label">Project URL</label>
                                                    <EditableText
                                                        isEditing={isEditing}
                                                        tagName="p"
                                                        className="modern-project-url-input"
                                                        initialValue={project.url || "https://example.com"}
                                                        onSave={(v) => handleUpdate(index, 'url', v)}
                                                    />
                                                </div>
                                            ) : project.url && (
                                                <Button asChild variant="outline" size="lg" className="modern-button-secondary">
                                                    <a href={project.url} target="_blank" rel="noopener noreferrer" className="modern-button-content">
                                                        View Project
                                                        <ExternalLink className="modern-w-4 modern-h-4" />
                                                    </a>
                                                </Button>
                                            )}

                                            {isEditing && (
                                                <Button
                                                    variant="destructive"
                                                    size="lg"
                                                    className="modern-button-danger"
                                                    onClick={() => dispatch!({ type: 'DELETE_PROJECT', payload: { id: project.id } })}
                                                >
                                                    <Trash className="modern-w-4 modern-h-4 modern-button-icon" />
                                                    Delete Project
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* Add project button */}
                {isEditing && (
                    <div className="modern-projects-add">
                        <Button
                            onClick={() => dispatch!({ type: 'ADD_PROJECT' })}
                            size="lg"
                            className="modern-button-primary"
                        >
                            Add New Project
                        </Button>
                    </div>
                )}
            </div>
        </section>
    );
}