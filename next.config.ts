import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    // --- THIS IS THE FIX ---
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'i.stack.imgur.com',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com'
      },
      {
        protocol: 'https',
        hostname: "res.cloudinary.com"
      }
    ],
  },
};

export default nextConfig;
