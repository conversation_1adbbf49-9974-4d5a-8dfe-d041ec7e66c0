"use client";
import { useAuthStore } from "@/stores/auth-store";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { LayoutDashboard, Briefcase, FileText, LogOut, Loader2 } from "lucide-react";
import { auth } from "@/lib/firebase";
import { signOut } from "firebase/auth";
import { toast } from "sonner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const deleteCookie = (name: string) => {
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

export default function PrivateLayout({ children }: { children: React.ReactNode }) {
  const { user, isLoaded } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (isLoaded && !user) {
      router.replace("/login");
    }
  }, [user, isLoaded, router]);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      deleteCookie('firebaseIdToken');
      toast.success("You have been signed out.");
      router.push("/");
    } catch {
      toast.error("Failed to sign out.");
    }
  };

  const navLinks = [
    { href: "/dashboard", label: "Dashboard", icon: LayoutDashboard },
    { href: "/portfolio", label: "Portfolio", icon: Briefcase },
    { href: "/resume", label: "Resume", icon: FileText },
  ];

  if (!isLoaded || !user) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex min-h-screen">
      <aside className="w-64 bg-background p-4 flex-col justify-between border-r hidden md:flex">
        <div>
          <h2 className="text-2xl font-bold mb-8">Profolify</h2>
          <nav className="flex flex-col space-y-1">
            {navLinks.map((link) => {
              const isActive = pathname === link.href;
              return (
                <Link key={link.href} href={link.href} passHref>
                  <Button variant={isActive ? "secondary" : "ghost"} className="w-full justify-start">
                    <link.icon className="mr-3 h-5 w-5" /> {link.label}
                  </Button>
                </Link>
              );
            })}
          </nav>
        </div>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2 border-t pt-4">
            <Avatar>
              <AvatarImage src={user.photoURL || ''} alt={user.displayName || 'User'} />
              <AvatarFallback>{user.displayName?.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex-1 overflow-hidden">
              <p className="text-sm font-medium truncate">{user.displayName}</p>
              <p className="text-xs text-muted-foreground truncate">{user.email}</p>
            </div>
          </div>
          <Button variant="outline" className="w-full" onClick={handleSignOut}>
            <LogOut className="mr-2 h-4 w-4" /> Sign Out
          </Button>
        </div>
      </aside>
      <main className="flex-1 p-8 overflow-y-auto bg-muted/40">{children}</main>
    </div>
  );
}