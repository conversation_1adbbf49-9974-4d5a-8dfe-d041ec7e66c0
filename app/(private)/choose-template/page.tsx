"use client";
import { templates } from "@/lib/templates";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createPortfolioFromTemplate } from "@/lib/portfolio-api";
import { useAuthStore } from "@/stores/auth-store";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { useState } from "react";

export default function ChooseTemplatePage() {
    const { user } = useAuthStore();
    const router = useRouter();
    const queryClient = useQueryClient();

    const [loadingTemplateId, setLoadingTemplateId] = useState<string | null>(null);

    const { mutate: selectTemplate } = useMutation({
        mutationFn: createPortfolioFromTemplate,

        onMutate: (variables) => {
            // When a mutation starts, set the loading ID
            setLoadingTemplateId(variables.templateId);
        },

        onSuccess: (newPortfolio) => {
            // --- Proactively update the TanStack Query cache ---
            // This places the new data directly into the cache, eliminating race conditions.
            queryClient.setQueryData(['portfolio', user?.uid], newPortfolio);
            router.push('/portfolio');
            toast.success("Portfolio created! Let's get started.");

        },
        onError: () => {
            toast.error("Failed to create portfolio. Please try again.");
        },
        onSettled: () => {
            // When the mutation is finished (success or error), clear the loading ID
            setLoadingTemplateId(null);
        }
    });

    const handleSelect = (templateId: string) => {
        if (!user) {
            toast.error("You must be logged in to select a template.");
            return;
        }
        selectTemplate({
            userId: user.uid,
            userEmail: user.email,
            templateId
        });
    };



    return (
        <div className="container mx-auto py-8">
            <div className="text-center mb-12">
                <h1 className="text-4xl font-bold">Choose Your Starting Point</h1>
                <p className="text-lg text-muted-foreground mt-2">Select a template to begin building your portfolio.</p>
            </div>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                {templates.map((template) => {
                    // Check if THIS specific template is the one loading
                    const isLoadingThisTemplate = loadingTemplateId === template.id;
                    return (
                        <Card key={template.id} className="flex flex-col">
                            <CardHeader>
                                <CardTitle>{template.name}</CardTitle>
                                <CardDescription>{template.description}</CardDescription>
                            </CardHeader>
                            <CardContent className="flex-grow">
                                <div className="aspect-video relative bg-muted rounded-md overflow-hidden">
                                    <Image src={template.thumbnailUrl} alt={template.name} fill className="object-cover" />
                                </div>
                            </CardContent>
                            <CardFooter>
                                <Button
                                    className="w-full"
                                    onClick={() => handleSelect(template.id)}
                                    // Disable ALL buttons if ANY template is being created
                                    disabled={!!loadingTemplateId}
                                >
                                    {isLoadingThisTemplate ? <Loader2 className="animate-spin mr-2" /> : null}
                                    Select {template.name}
                                </Button>
                            </CardFooter>
                        </Card>
                    );
                })}
            </div>
        </div>
    );
}