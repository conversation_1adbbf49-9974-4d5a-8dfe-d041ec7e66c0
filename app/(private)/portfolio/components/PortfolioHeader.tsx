"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SaveStatusIndicator } from "@/components/ui/SaveStatusIndicator";
import { useEditor } from "@/contexts/EditorContext";
import { Eye, Rocket, Trash2, Loader2 } from "lucide-react";

interface PortfolioHeaderProps {
    isPublishing: boolean;
    isDeleting: boolean;
    isDirty: boolean; // --- NEW: Prop to track unsaved changes ---
    onPreview: () => void;
    onDelete: () => void;
    onTogglePublish: () => void;
}

export function PortfolioHeader({
    isPublishing,
    isDeleting,
    isDirty, // --- NEW ---
    onPreview,
    onDelete,
    onTogglePublish
}: PortfolioHeaderProps) {
    const { state } = useEditor();
    const { saveStatus, formData } = state;
    const isActionPending = isPublishing || isDeleting;

    // --- NEW: Logic to determine if the publish button should be disabled ---
    // Disable if:
    // 1. An action is already pending.
    // 2. The portfolio is already published AND there are no new changes (it's not dirty).
    const isPublishButtonDisabled = isActionPending || (formData.isPublished && !isDirty);

    return (
        <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4 w-full">
            <div>
                <h1 className="text-2xl font-bold">Portfolio Editor</h1>
                <p className="text-sm text-muted-foreground">Your changes are saved automatically.</p>
            </div>
            <div className="flex items-center gap-2 md:gap-4">
                <SaveStatusIndicator status={saveStatus} />
                <Button variant="outline" onClick={onPreview} disabled={isActionPending}>
                    <Eye className="mr-2 h-4 w-4" /> Preview
                </Button>
                <Button variant="destructive" onClick={onDelete} disabled={isActionPending}>
                    {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2 className="mr-2 h-4 w-4" />} Discard
                </Button>
                <Button onClick={onTogglePublish} disabled={isPublishButtonDisabled}> {/* --- FIX: Use the new disabled logic --- */}
                    {isPublishing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Rocket className="mr-2 h-4 w-4" />}
                    {formData.isPublished ? "Update Live" : "Publish"}
                </Button>
            </div>
        </div>
    );
}