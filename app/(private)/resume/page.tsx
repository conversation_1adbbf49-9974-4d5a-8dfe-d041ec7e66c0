"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

export default function ResumePage() {

  const handleExport = () => {
    // Placeholder function for LaTeX PDF export
    // In a real app, this would call a serverless function (e.g., on Vercel or Firebase Cloud Functions)
    // The function would take the form data, generate a .tex file, compile it to PDF, and return a download link.
    toast.info("Feature in development!", {
      description: "LaTeX PDF export is coming soon.",
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Resume Builder</h1>
        <Button onClick={handleExport}>Export as PDF (LaTeX)</Button>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Enter Your Resume Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fullName">Full Name</Label>
              <Input id="fullName" placeholder="Jane Doe" />
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" />
            </div>
          </div>
          <div>
            <Label htmlFor="summary">Professional Summary</Label>
            <Textarea id="summary" placeholder="A brief summary of your skills and experience..." />
          </div>
          {/* Add more fields for education, experience, skills etc. as needed */}
        </CardContent>
      </Card>
    </div>
  );
}