"use client";

import { useAuthStore } from "@/stores/auth-store";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Eye, Sparkles, Loader2, Download } from "lucide-react";
import { PortfolioData } from "@/lib/types";
import { Input } from "@/components/ui/input";
import { getPortfolio } from "@/lib/portfolio-api";
import { auth } from "@/lib/firebase"; // Import client-side auth
import { toast } from "sonner";
import { useState } from "react";

export default function DashboardPage() {
  const { user } = useAuthStore();
  const [isExporting, setIsExporting] = useState(false); // State for export loader

  const { data: portfolio, isLoading } = useQuery<PortfolioData | null>({ // Can be null now
    queryKey: ["portfolio", user?.uid],
    queryFn: () => getPortfolio(user!.uid), // Use getPortfolio
    enabled: !!user,
  });

  const handlePreview = () => {
    if (!portfolio) return;
    localStorage.setItem('portfolio-preview', JSON.stringify(portfolio));
    window.open('/portfolio/preview', '_blank');
  };


  const handleExport = async () => {
    if (!user || !auth.currentUser) {
      toast.error("Authentication error. Please re-login.");
      return;
    }
    setIsExporting(true);
    toast.info("Starting portfolio export... This may take a moment.");

    try {
      const token = await auth.currentUser.getIdToken();
      const response = await fetch('/api/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || "An unknown error occurred during export.");
      }

      // This is the standard browser logic to trigger a file download from a blob
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${portfolio?.slug || 'portfolio'}.zip`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      toast.success("Export successful! Check your downloads.");

    } catch (error: any) {
      toast.error(`Export failed: ${error.message}`);
    } finally {
      setIsExporting(false);
    }
  };

  if (isLoading) {
    return <div className="flex h-full items-center justify-center"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <div>
      <h1 className="text-3xl font-bold">Welcome back, {user?.displayName}!</h1>

      {/* --- UI for users WITH a portfolio --- */}
      {portfolio && (
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Your Portfolio</CardTitle>
              <CardDescription>
                Your portfolio is currently {portfolio.isPublished ? 'published and live' : 'a draft'}.
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col sm:flex-row gap-4">
              <Input
                readOnly
                value={`${typeof window !== 'undefined' ? window.location.origin : ''}/${portfolio.slug}`}
                className="flex-grow"
              />

              <div className="flex gap-2">
                <Button asChild variant="secondary">
                  <Link href="/portfolio">Edit Portfolio</Link>
                </Button>
                {/* --- The New Export Button --- */}
                <Button onClick={handleExport} variant="outline" disabled={isExporting}>
                  {isExporting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="mr-2 h-4 w-4" />}
                  Export Site
                </Button>
                {portfolio.isPublished ? (
                  <Button asChild>
                    <Link href={`/${portfolio.slug}`} target="_blank">View Live</Link>
                  </Button>
                ) : (
                  <Button onClick={handlePreview} variant="outline">
                    <Eye className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* --- UI for users WITHOUT a portfolio --- */}
      {!portfolio && !isLoading && (
        <Card className="mt-8 bg-gradient-to-br from-accent/10 to-transparent">
          <CardHeader>
            <Sparkles className="h-8 w-8 text-accent mb-2" />
            <CardTitle className="text-2xl">Create Your Portfolio</CardTitle>
            <CardDescription>
              Showcase your skills with a beautiful portfolio. Get started by choosing a template.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/choose-template">
              <Button size="lg">
                Choose a Template <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
}