import { getPortfolioBySlug } from '@/lib/portfolio-api';
import { notFound } from 'next/navigation';

// --- NEW: Import all possible theme components ---
import { ProfolifyTheme } from '@/components/portfolio-themes/ProfolifyTheme';
import { ModernTheme } from '@/components/portfolio-themes/ModernTheme';

export default async function PublicPortfolioPage({ params }: { params: { slug: string } }) {
    // 1. Fetch the data (no change here)
    const portfolioData = await getPortfolioBySlug(params.slug);

    // 2. Handle the case where the portfolio is not found or not published
    if (!portfolioData) {
        notFound();
    }

    // 3. --- FIX: Use a switch statement to render the correct theme ---
    // This is the core of the solution. We check the templateId from the database.
    switch (portfolioData.templateId) {
        case 'modern-theme-v1':
            return <ModernTheme isEditing={false} serverData={portfolioData} />;

        case 'profolify-theme-v1':
            return <ProfolifyTheme isEditing={false} serverData={portfolioData} />;

        // A fallback case is good practice. If the templateId is unknown,
        // it will render the default theme.
        default:
            return <ProfolifyTheme isEditing={false} serverData={portfolioData} />;
    }
}