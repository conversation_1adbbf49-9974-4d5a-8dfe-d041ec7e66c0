import type { Metadata } from "next";
import "./globals.css"; // Your globals.css should be imported here
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { GeistSans } from "geist/font/sans"; // Import the sans-serif font
import { GeistMono } from "geist/font/mono"; // Import the monospaced font
import { AuthProvider } from "@/components/providers/auth-provider";
import { QueryProvider } from "@/components/providers/query-provider";


export const metadata: Metadata = {
  title: "Profolify - Build Your Professional Portfolio",
  description: "Create and share your professional portfolio and resume with ease.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`} suppressHydrationWarning>
      <body >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <QueryProvider>
              {children}
              <Toaster />
            </QueryProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}