@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Modern Brand Color System */
  --color-backgroundPrimary: var(--background-primary);
  --color-backgroundSecondary: var(--background-secondary);
  --color-backgroundTertiary: var(--background-tertiary);
  --color-textPrimary: var(--text-primary);
  --color-textSecondary: var(--text-secondary);
  --color-textMuted: var(--text-muted);
  --color-brandPrimary: var(--brand-primary);
  --color-brandSecondary: var(--brand-secondary);
  --color-brandAccent: var(--brand-accent);
  --color-brandGradientStart: var(--brand-gradient-start);
  --color-brandGradientEnd: var(--brand-gradient-end);
  --color-borderPrimary: var(--border-primary);
  --color-borderSecondary: var(--border-secondary);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-glassBackground: var(--glass-background);
  --color-shadowPrimary: var(--shadow-primary);
  --color-shadowSecondary: var(--shadow-secondary);
}

:root {
  --radius: 0.75rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.396 0.115 266.89);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.396 0.115 266.89);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.396 0.115 266.89);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.396 0.115 266.89);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.396 0.115 266.89);

  /* Modern Color System - Light Mode */
  --background-primary: #ffffff;
  --background-secondary: #fafbfc;
  --background-tertiary: #f5f6f8;
  --text-primary: #1a1d21;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --brand-primary: #6366f1;
  --brand-secondary: #8b5cf6;
  --brand-accent: #06d6a0;
  --brand-gradient-start: #6366f1;
  --brand-gradient-end: #8b5cf6;
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e0;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --glass-background: rgba(255, 255, 255, 0.85);
  --shadow-primary: rgba(0, 0, 0, 0.05);
  --shadow-secondary: rgba(0, 0, 0, 0.1);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.15 0.02 264.695);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.15 0.02 264.695);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.704 0.191 266.89);
  --primary-foreground: oklch(0.15 0.02 264.695);
  --secondary: oklch(0.208 0.042 265.755);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.208 0.042 265.755);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.704 0.191 266.89);
  --accent-foreground: oklch(0.15 0.02 264.695);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.208 0.042 265.755);
  --input: oklch(0.208 0.042 265.755);
  --ring: oklch(0.704 0.191 266.89);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.15 0.02 264.695);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.704 0.191 266.89);
  --sidebar-primary-foreground: oklch(0.15 0.02 264.695);
  --sidebar-accent: oklch(0.208 0.042 265.755);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(0.208 0.042 265.755);
  --sidebar-ring: oklch(0.704 0.191 266.89);

  /* Modern Color System - Dark Mode */
  --background-primary: #0a0b0d;
  --background-secondary: #131417;
  --background-tertiary: #1a1b1f;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --brand-primary: #818cf8;
  --brand-secondary: #a78bfa;
  --brand-accent: #34d399;
  --brand-gradient-start: #818cf8;
  --brand-gradient-end: #a78bfa;
  --border-primary: #2d3748;
  --border-secondary: #4a5568;
  --success: #34d399;
  --warning: #fbbf24;
  --error: #f87171;
  --glass-background: rgba(10, 11, 13, 0.85);
  --shadow-primary: rgba(0, 0, 0, 0.2);
  --shadow-secondary: rgba(0, 0, 0, 0.4);
}

@layer base {
  * {
    @apply border-borderPrimary outline-ring/50;
  }

  body {
    @apply bg-backgroundPrimary text-textPrimary;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .glass-effect {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--glass-background);
    border: 1px solid var(--border-primary);
  }

  .gradient-text {
    background: linear-gradient(
      135deg,
      var(--brand-gradient-start),
      var(--brand-gradient-end)
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-border {
    background: linear-gradient(
          var(--background-primary),
          var(--background-primary)
        )
        padding-box,
      linear-gradient(
          135deg,
          var(--brand-gradient-start),
          var(--brand-gradient-end)
        )
        border-box;
    border: 2px solid transparent;
  }

  /* Gradient button with proper text contrast */
  .btn-gradient {
    background: linear-gradient(
      135deg,
      var(--brand-primary),
      var(--brand-secondary)
    ) !important;
    color: #ffffff !important;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-gradient:hover {
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.25);
    transform: translateY(-2px);
    color: #ffffff !important;
  }

  /* Force white text on all gradient buttons with maximum specificity */
  .gradient-button-text,
  .gradient-button-text *,
  .gradient-button-text span,
  .gradient-button-text svg,
  button.gradient-button-text,
  button.gradient-button-text *,
  button.gradient-button-text span,
  button.gradient-button-text svg {
    color: #ffffff !important;
    fill: #ffffff !important;
  }

  .gradient-button-text:hover,
  .gradient-button-text:hover *,
  .gradient-button-text:hover span,
  .gradient-button-text:hover svg,
  button.gradient-button-text:hover,
  button.gradient-button-text:hover *,
  button.gradient-button-text:hover span,
  button.gradient-button-text:hover svg {
    color: #ffffff !important;
    fill: #ffffff !important;
  }

  /* Override any potential conflicting button styles */
  button[class*="gradient-button-text"] {
    color: #ffffff !important;
  }

  button[class*="gradient-button-text"] * {
    color: #ffffff !important;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease,
    color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--brand-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--brand-secondary);
}

/* Ensure proper spacing for fixed navbar */
body {
  scroll-padding-top: 80px;
}
