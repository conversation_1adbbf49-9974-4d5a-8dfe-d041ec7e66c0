"use client";
import { createContext, useContext } from 'react';

// Create a context that will hold a simple boolean value.
const ExportContext = createContext<boolean>(false);

// Export the provider component. We'll use this in our API route and our live editor.
export const ExportProvider = ExportContext.Provider;

// Export a custom hook that components can use to easily access the value.
export function useIsExport(): boolean {
    return useContext(ExportContext);
}