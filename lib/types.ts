export interface Project {
  id: string;
  title: string;
  description: string;
  url?: string;
  imageUrl?: string;
}

export interface Experience {
  id: string;
  role: string;
  company: string;
  duration: string;
}

export interface SocialLinks {
  github?: string;
  linkedin?: string;
  twitter?: string;
}




export interface PortfolioData {
  uid: string;
  isPublished: boolean;
  slug: string;
  templateId: string;
  userName: string;
  profession: string;
  about?: string; // --- NEW FIELD ---
  profileImageUrl?: string;
  resumeUrl?: string;
  projects: Project[];
  experiences: Experience[];
  socials: SocialLinks;
  contactEmail: string;
}

export const defaultPortfolioData = (uid: string, email: string | null): PortfolioData => ({
  uid,
  isPublished: false,
  slug: uid, // Default slug is the user's ID
  templateId: 'profolify-theme-v1',
  userName: 'Your Name',
  profession: 'Your Profession',
  contactEmail: email || '<EMAIL>',
  profileImageUrl: '',
  resumeUrl: '',
  projects: [],
  experiences: [],
  socials: {
    github: 'https://github.com',
    linkedin: 'https://linkedin.com',
    twitter: 'https://twitter.com'
  },
});