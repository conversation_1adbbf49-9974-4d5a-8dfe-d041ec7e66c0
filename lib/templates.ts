import { PortfolioData } from "./types";

export interface Template {
    id: string;
    name: string;
    description: string;
    thumbnailUrl: string;
}

export const templates: Template[] = [
    {
        id: 'profolify-theme-v1',
        name: 'Classic Professional',
        description: 'A clean, classic layout perfect for any profession. Simple and elegant.',
        thumbnailUrl: 'https://placehold.co/600x400/27272a/fafafa?text=Classic',
    },
    {
        id: 'modern-theme-v1',
        name: 'Modern Creative',
        description: 'A bold, modern design with a dark theme, ideal for developers and designers.',
        thumbnailUrl: 'https://placehold.co/600x400/fafafa/27272a?text=Modern',
    }
];

// Returns the full default data structure for a new portfolio based on template ID
export const getTemplateDefaultData = (templateId: string, uid: string, email: string | null): PortfolioData => {
    // Shared base data
    const baseData = {
        uid,
        isPublished: false,
        slug: uid,
        contactEmail: email || '<EMAIL>',
        profileImageUrl: '',
        resumeUrl: '',
        experiences: [],
        socials: {
            github: 'https://github.com/your-username',
            linkedin: 'https://linkedin.com/in/your-username',
            twitter: 'https://twitter.com/your-username',
        },
    };

    switch (templateId) {
        case 'modern-theme-v1':
            return {
                ...baseData,
                templateId: 'modern-theme-v1',
                userName: 'Your Name',
                profession: 'Creative Developer',
                about: 'I am a passionate developer with a love for building beautiful, intuitive, and high-performance web applications. I thrive in creative environments.', // --- NEW ---
                projects: [
                    { id: '1', title: 'Project One', description: 'A sleek and modern web application.', imageUrl: '' },
                ],
            };
        case 'profolify-theme-v1':
        default:
            return {
                ...baseData,
                templateId: 'profolify-theme-v1',
                userName: 'Your Name',
                profession: 'Web Developer & Specialist',
                about: 'A brief and professional summary about your skills, experience, and career goals. Let potential employers know what makes you the right choice.', // --- NEW ---
                projects: [
                    { id: '1', title: 'First Project', description: 'A detailed overview of my work and its impact.', imageUrl: '' },
                ],
            };
    }
};